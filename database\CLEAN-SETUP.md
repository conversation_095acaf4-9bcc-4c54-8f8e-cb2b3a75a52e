# إعداد قاعدة البيانات المبسط

## الملفات المتبقية

### Seeders
- `DatabaseSeeder.php` - ينشئ مستخدمين فقط
- `CountrySeeder.php` - ينشئ جميع الدول

### Factories
- `UserFactory.php` - لإنشاء المستخدمين

## المستخدمين المنشأين

### مستخدم إداري
- **الاسم:** Admin User
- **الإيميل:** <EMAIL>
- **كلمة المرور:** password
- **النوع:** إداري (type = 1)

### مستخدم عادي
- **الاسم:** Test User
- **الإيميل:** <EMAIL>
- **كلمة المرور:** password
- **النوع:** مستخدم عادي (type = 0)

## للتشغيل

```bash
# تشغيل المايجريشن والسيدرز
php artisan migrate:fresh --seed
```

## النتيجة

سيتم إنشاء:
- جميع الدول (260 دولة) - يتم تشغيل CountrySeeder أولاً
- مستخدمين اثنين فقط مرتبطين بمصر كدولة افتراضية

## تسجيل الدخول

يمكنك تسجيل الدخول باستخدام:

**للإداري:**
- Email: <EMAIL>
- Password: password

**للمستخدم العادي:**
- Email: <EMAIL>
- Password: password

---

تم تنظيف جميع الملفات غير الضرورية وترك الأساسيات فقط! ✅
