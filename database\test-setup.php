<?php

/**
 * اختبار سريع للإعداد المبسط
 * 
 * لتشغيل هذا الاختبار:
 * php artisan tinker
 * ثم انسخ والصق الكود أدناه
 */

echo "🧪 اختبار الإعداد المبسط...\n\n";

// 1. اختبار إنشاء المستخدمين
echo "👥 اختبار المستخدمين...\n";

$users = \App\Models\User::all();
echo "✅ عدد المستخدمين: " . $users->count() . "\n";

foreach ($users as $user) {
    $userType = $user->type == 1 ? 'إداري' : 'مستخدم عادي';
    echo "   - {$user->name} ({$user->email}) - {$userType}\n";
}

// 2. اختبار الدول
echo "\n🌍 اختبار الدول...\n";
$countries = \App\Models\Country::all();
echo "✅ عدد الدول: " . $countries->count() . "\n";

// عرض بعض الدول كمثال
$sampleCountries = $countries->take(5);
foreach ($sampleCountries as $country) {
    echo "   - {$country->name} ({$country->code})\n";
}

echo "\n📊 ملخص الإعداد:\n";
echo "✅ المستخدمين: " . \App\Models\User::count() . "\n";
echo "✅ الدول: " . \App\Models\Country::count() . "\n";
echo "✅ التصنيفات: " . \App\Models\Category::count() . "\n";
echo "✅ العلامات التجارية: " . \App\Models\Brand::count() . "\n";
echo "✅ المنتجات: " . \App\Models\Product::count() . "\n";

echo "\n🔑 بيانات تسجيل الدخول:\n";
echo "   الإداري: <EMAIL> / password\n";
echo "   المستخدم: <EMAIL> / password\n";

echo "\n🎉 الإعداد المبسط جاهز للاستخدام!\n";

/**
 * للتشغيل:
 * 
 * 1. تشغيل المايجريشن والسيدرز:
 *    php artisan migrate:fresh --seed
 * 
 * 2. اختبار الإعداد:
 *    php artisan tinker
 *    ثم انسخ والصق الكود أعلاه
 */
