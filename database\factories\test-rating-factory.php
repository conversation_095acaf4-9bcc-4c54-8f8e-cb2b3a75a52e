<?php

/**
 * اختبار سريع لـ ProductRatingFactory المحدث
 * 
 * لتشغيل هذا الاختبار:
 * php artisan tinker
 * ثم انسخ والصق الكود أدناه
 */

echo "⭐ اختبار ProductRatingFactory المحدث...\n\n";

// 1. إنشاء منتج للاختبار
echo "🔧 إنشاء منتج للاختبار...\n";
try {
    $product = \App\Models\Product::factory()->create();
    echo "✅ تم إنشاء منتج: {$product->name['ar']} - ID: {$product->id}\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء المنتج: " . $e->getMessage() . "\n";
    exit;
}
echo "\n";

// 2. اختبار تقييم عادي
echo "📝 اختبار التقييم العادي...\n";
try {
    $rating = \App\Models\ProductRating::factory()->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء تقييم عادي بنجاح - ID: {$rating->id}\n";
    echo "   - المستخدم: {$rating->username}\n";
    echo "   - التقييم: {$rating->rating}/5\n";
    echo "   - التعليق: {$rating->comment}\n";
    echo "   - الحالة: " . ($rating->status ? 'مقبول' : 'معلق') . "\n";
    echo "   - طول التعليق: " . strlen($rating->comment) . " حرف\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء التقييم العادي: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. اختبار تقييم مقبول
echo "✅ اختبار التقييم المقبول...\n";
try {
    $approvedRating = \App\Models\ProductRating::factory()->approved()->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء تقييم مقبول بنجاح - ID: {$approvedRating->id}\n";
    echo "   - الحالة: " . ($approvedRating->status ? 'مقبول' : 'معلق') . "\n";
    echo "   - التقييم: {$approvedRating->rating}/5\n";
    echo "   - التعليق: {$approvedRating->comment}\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء التقييم المقبول: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. اختبار تقييم معلق
echo "⏳ اختبار التقييم المعلق...\n";
try {
    $pendingRating = \App\Models\ProductRating::factory()->pending()->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء تقييم معلق بنجاح - ID: {$pendingRating->id}\n";
    echo "   - الحالة: " . ($pendingRating->status ? 'مقبول' : 'معلق') . "\n";
    echo "   - التقييم: {$pendingRating->rating}/5\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء التقييم المعلق: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. اختبار تقييم عالي
echo "🌟 اختبار التقييم العالي...\n";
try {
    $highRating = \App\Models\ProductRating::factory()->approved()->highRating()->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء تقييم عالي بنجاح - ID: {$highRating->id}\n";
    echo "   - التقييم: {$highRating->rating}/5\n";
    echo "   - التعليق: {$highRating->comment}\n";
    echo "   - طول التعليق: " . strlen($highRating->comment) . " حرف\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء التقييم العالي: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. اختبار تقييم منخفض
echo "⭐ اختبار التقييم المنخفض...\n";
try {
    $lowRating = \App\Models\ProductRating::factory()->approved()->lowRating()->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء تقييم منخفض بنجاح - ID: {$lowRating->id}\n";
    echo "   - التقييم: {$lowRating->rating}/5\n";
    echo "   - التعليق: {$lowRating->comment}\n";
    echo "   - طول التعليق: " . strlen($lowRating->comment) . " حرف\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء التقييم المنخفض: " . $e->getMessage() . "\n";
}
echo "\n";

// 7. اختبار إنشاء عدة تقييمات
echo "📊 اختبار إنشاء عدة تقييمات...\n";
try {
    $multipleRatings = \App\Models\ProductRating::factory(5)->approved()->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء {$multipleRatings->count()} تقييمات بنجاح\n";
    
    foreach ($multipleRatings as $rating) {
        echo "   - تقييم {$rating->id}: {$rating->rating}/5 - طول التعليق: " . strlen($rating->comment) . " حرف\n";
    }
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء التقييمات المتعددة: " . $e->getMessage() . "\n";
}
echo "\n";

// 8. اختبار طول التعليقات
echo "📏 اختبار طول التعليقات...\n";
$allRatings = \App\Models\ProductRating::where('product_id', $product->id)->get();
$maxLength = 0;
$minLength = 999;

foreach ($allRatings as $rating) {
    $length = strlen($rating->comment);
    if ($length > $maxLength) $maxLength = $length;
    if ($length < $minLength) $minLength = $length;
}

echo "✅ تحليل أطوال التعليقات:\n";
echo "   - أطول تعليق: {$maxLength} حرف\n";
echo "   - أقصر تعليق: {$minLength} حرف\n";
echo "   - الحد الأقصى المسموح: 255 حرف\n";

if ($maxLength <= 255) {
    echo "✅ جميع التعليقات ضمن الحد المسموح\n";
} else {
    echo "❌ بعض التعليقات تتجاوز الحد المسموح\n";
}
echo "\n";

// 9. عرض ملخص الاختبار
echo "📊 ملخص الاختبار:\n";
echo "✅ جميع أنواع التقييمات تم إنشاؤها بنجاح\n";
echo "✅ التعليقات باللغة العربية وواقعية\n";
echo "✅ أطوال التعليقات ضمن الحد المسموح (255 حرف)\n";
echo "✅ الحالات (States) تعمل كما هو متوقع\n";
echo "✅ التقييمات مرتبطة بالمنتجات بشكل صحيح\n\n";

echo "🎉 ProductRatingFactory جاهز للاستخدام!\n";

/**
 * للتشغيل:
 * 
 * 1. تأكد من تشغيل المايجريشن:
 *    php artisan migrate:fresh
 * 
 * 2. افتح Laravel Tinker:
 *    php artisan tinker
 * 
 * 3. انسخ والصق الكود أعلاه
 * 
 * أو شغل السيدرز مباشرة:
 *    php artisan db:seed --class=ProductRatingSeeder
 */
