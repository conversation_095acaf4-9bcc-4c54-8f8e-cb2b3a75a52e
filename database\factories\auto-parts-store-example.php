<?php

/**
 * مثال شامل لإنشاء متجر قطع غيار سيارات باستخدام الـ Factories
 * 
 * هذا المثال يوضح كيفية إنشاء متجر قطع غيار كامل مع:
 * - منتجات واقعية بأسعار منطقية
 * - مستخدمين وإداريين
 * - طلبات وعناصر طلبات
 * - تقييمات المنتجات
 * - كوبونات خصم
 * - عربة التسوق وقائمة الأمنيات
 * 
 * لتشغيل هذا المثال:
 * php artisan tinker
 * ثم انسخ والصق الكود أدناه
 */

echo "🚗 إنشاء متجر قطع غيار السيارات...\n\n";

// 1. إنشاء الدول
echo "📍 إنشاء الدول...\n";
$egypt = \App\Models\Country::factory()->egypt()->create();
$usa = \App\Models\Country::factory()->usa()->create();
echo "✅ تم إنشاء {$egypt->name} و {$usa->name}\n\n";

// 2. إنشاء إعدادات الموقع
echo "⚙️ إنشاء إعدادات الموقع...\n";
$siteSettings = \App\Models\SiteSetting::factory()->arabic()->create();
echo "✅ تم إنشاء إعدادات الموقع: {$siteSettings->site_name['ar']}\n\n";

// 3. إنشاء التصنيفات والعلامات التجارية
echo "🏷️ إنشاء التصنيفات والعلامات التجارية...\n";
$categories = \App\Models\Category::factory(8)->create();
$brands = \App\Models\Brand::factory(12)->create();
echo "✅ تم إنشاء {$categories->count()} تصنيفات و {$brands->count()} علامة تجارية\n\n";

// 4. إنشاء المستخدمين
echo "👥 إنشاء المستخدمين...\n";
$admin = \App\Models\User::factory()->admin()->create([
    'name' => 'مدير المتجر',
    'email' => '<EMAIL>',
    'country_id' => $egypt->id,
]);

$customers = \App\Models\User::factory(25)->create([
    'country_id' => $egypt->id,
]);

echo "✅ تم إنشاء مدير و {$customers->count()} عميل\n\n";

// 5. إنشاء المنتجات (قطع الغيار)
echo "🔧 إنشاء قطع الغيار...\n";

// منتجات عادية
$regularProducts = \App\Models\Product::factory(30)->create();

// منتجات رائجة
$trendingProducts = \App\Models\Product::factory(8)->trending()->create();

// منتجات مخفضة
$saleProducts = \App\Models\Product::factory(6)->onSale()->create();

// منتجات فاخرة
$premiumProducts = \App\Models\Product::factory(4)->premium()->create();

// منتجات اقتصادية
$budgetProducts = \App\Models\Product::factory(12)->budget()->create();

// منتجات بكمية قليلة
$lowStockProducts = \App\Models\Product::factory(5)->lowStock()->create();

$totalProducts = $regularProducts->count() + $trendingProducts->count() + 
                $saleProducts->count() + $premiumProducts->count() + 
                $budgetProducts->count() + $lowStockProducts->count();

echo "✅ تم إنشاء {$totalProducts} منتج:\n";
echo "   - {$regularProducts->count()} منتج عادي\n";
echo "   - {$trendingProducts->count()} منتج رائج\n";
echo "   - {$saleProducts->count()} منتج مخفض\n";
echo "   - {$premiumProducts->count()} منتج فاخر\n";
echo "   - {$budgetProducts->count()} منتج اقتصادي\n";
echo "   - {$lowStockProducts->count()} منتج بكمية قليلة\n\n";

// 6. إضافة صور للمنتجات
echo "📸 إضافة صور المنتجات...\n";
$allProducts = \App\Models\Product::all();
$totalImages = 0;
foreach ($allProducts as $product) {
    $imageCount = rand(1, 4);
    \App\Models\ProductImage::factory($imageCount)->create([
        'product_id' => $product->id
    ]);
    $totalImages += $imageCount;
}
echo "✅ تم إضافة {$totalImages} صورة للمنتجات\n\n";

// 7. إنشاء كوبونات الخصم
echo "🎫 إنشاء كوبونات الخصم...\n";
$activeCoupons = \App\Models\DiscountCoupon::factory(5)->active()->create([
    'created_by' => $admin->id,
]);
$expiredCoupons = \App\Models\DiscountCoupon::factory(2)->expired()->create([
    'created_by' => $admin->id,
]);
echo "✅ تم إنشاء {$activeCoupons->count()} كوبون نشط و {$expiredCoupons->count()} كوبون منتهي الصلاحية\n\n";

// 8. إنشاء رسوم الشحن
echo "🚚 إنشاء رسوم الشحن...\n";
$shippingCharges = \App\Models\ShippingCharges::factory()->create([
    'country_id' => $egypt->id,
    'amount' => 30.00, // 30 جنيه مصري
]);
echo "✅ تم إنشاء رسوم شحن: {$shippingCharges->amount} جنيه لمصر\n\n";

// 9. إنشاء عناصر عربة التسوق
echo "🛒 إنشاء عناصر عربة التسوق...\n";
$cartItems = 0;
foreach ($customers->take(15) as $customer) {
    $itemCount = rand(1, 5);
    $selectedProducts = $allProducts->random($itemCount);
    
    foreach ($selectedProducts as $product) {
        \App\Models\Cart::factory()->create([
            'user_id' => $customer->id,
            'product_id' => $product->id,
            'name' => $product->name['ar'],
            'selling_price' => $product->selling_price,
        ]);
        $cartItems++;
    }
}
echo "✅ تم إنشاء {$cartItems} عنصر في عربات التسوق\n\n";

// 10. إنشاء قوائم الأمنيات
echo "❤️ إنشاء قوائم الأمنيات...\n";
$wishlistItems = 0;
foreach ($customers->take(20) as $customer) {
    $itemCount = rand(2, 8);
    $selectedProducts = $allProducts->random($itemCount);
    
    foreach ($selectedProducts as $product) {
        \App\Models\Wishlist::factory()->create([
            'user_id' => $customer->id,
            'product_id' => $product->id,
        ]);
        $wishlistItems++;
    }
}
echo "✅ تم إنشاء {$wishlistItems} عنصر في قوائم الأمنيات\n\n";

// 11. إنشاء الطلبات
echo "📦 إنشاء الطلبات...\n";
$orders = \App\Models\Order::factory(20)->create();
$orderItems = 0;

foreach ($orders as $order) {
    $itemCount = rand(1, 4);
    $selectedProducts = $allProducts->random($itemCount);
    
    foreach ($selectedProducts as $product) {
        \App\Models\OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'name' => $product->name['ar'],
            'price' => $product->selling_price,
        ]);
        $orderItems++;
    }
}
echo "✅ تم إنشاء {$orders->count()} طلب مع {$orderItems} عنصر\n\n";

// 12. إنشاء تقييمات المنتجات
echo "⭐ إنشاء تقييمات المنتجات...\n";
$ratings = 0;
foreach ($allProducts->take(30) as $product) {
    $ratingCount = rand(2, 8);
    \App\Models\ProductRating::factory($ratingCount)->approved()->create([
        'product_id' => $product->id,
    ]);
    $ratings += $ratingCount;
}
echo "✅ تم إنشاء {$ratings} تقييم للمنتجات\n\n";

// 13. إنشاء عناوين العملاء
echo "📍 إنشاء عناوين العملاء...\n";
$addresses = 0;
foreach ($customers->take(15) as $customer) {
    $addressCount = rand(1, 3);
    \App\Models\CustomerAddress::factory($addressCount)->create([
        'user_id' => $customer->id,
        'country_id' => $egypt->id,
    ]);
    $addresses += $addressCount;
}
echo "✅ تم إنشاء {$addresses} عنوان للعملاء\n\n";

echo "🎉 تم إنشاء متجر قطع غيار السيارات بنجاح!\n\n";

echo "📊 ملخص المتجر:\n";
echo "👥 المستخدمين: " . \App\Models\User::count() . "\n";
echo "🔧 المنتجات: " . \App\Models\Product::count() . "\n";
echo "📸 الصور: " . \App\Models\ProductImage::count() . "\n";
echo "📦 الطلبات: " . \App\Models\Order::count() . "\n";
echo "🛒 عناصر العربة: " . \App\Models\Cart::count() . "\n";
echo "❤️ قائمة الأمنيات: " . \App\Models\Wishlist::count() . "\n";
echo "⭐ التقييمات: " . \App\Models\ProductRating::count() . "\n";
echo "🎫 كوبونات الخصم: " . \App\Models\DiscountCoupon::count() . "\n";
echo "📍 العناوين: " . \App\Models\CustomerAddress::count() . "\n";

echo "\n✨ المتجر جاهز للاستخدام!\n";

/**
 * لتشغيل هذا المثال:
 * 
 * 1. تأكد من تشغيل المايجريشن:
 *    php artisan migrate:fresh
 * 
 * 2. افتح Laravel Tinker:
 *    php artisan tinker
 * 
 * 3. انسخ والصق الكود أعلاه
 * 
 * أو يمكنك تشغيل الـ Seeders مباشرة:
 *    php artisan db:seed
 */
