<?php

namespace Database\Factories;

use App\Models\Cart;
use App\Models\User;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Cart>
 */
class CartFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'product_id' => Product::factory(),
            'name' => fake()->words(3, true),
            'qty' => fake()->numberBetween(1, 10),
            'selling_price' => fake()->randomFloat(2, 10, 500),
        ];
    }

    /**
     * Create cart item with specific quantity.
     *
     * @param int $quantity
     * @return $this
     */
    public function withQuantity(int $quantity): static
    {
        return $this->state(fn (array $attributes) => [
            'qty' => $quantity,
        ]);
    }

    /**
     * Create cart item for specific user.
     *
     * @param int $userId
     * @return $this
     */
    public function forUser(int $userId): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $userId,
        ]);
    }

    /**
     * Create cart item for specific product.
     *
     * @param int $productId
     * @return $this
     */
    public function forProduct(int $productId): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $productId,
        ]);
    }
}
