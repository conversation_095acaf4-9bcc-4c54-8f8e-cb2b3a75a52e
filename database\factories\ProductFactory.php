<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // قائمة بقطع غيار السيارات الحقيقية
        $autoPartNames = [
            'فلتر هواء',
            'فلتر زيت',
            'فلتر وقود',
            'فلتر مكيف',
            'فحمات فرامل أمامية',
            'فحمات فرامل خلفية',
            'أقراص فرامل',
            'زيت محرك',
            'زيت فتيس',
            'سائل فرامل',
            'سائل مكيف',
            'بطارية سيارة',
            'شمعات إشعال',
            'كويلات إشعال',
            'مصابيح أمامية',
            'مصابيح خلفية',
            'إطارات',
            'ممتص صدمات',
            'رديتر',
            'مروحة رديتر',
            'حساس أكسجين',
            'حساس حرارة',
            'طرمبة مياه',
            'طرمبة وقود',
            'سير مولد',
            'سير تكييف',
            'كلتش',
            'فلاي ويل',
            'جوان رأس',
            'جوان كارتير',
            'مرآة جانبية',
            'مقبض باب',
            'زجاج أمامي',
            'زجاج خلفي',
            'كشافات ضباب',
            'إشارات جانبية',
        ];

        $partName = fake()->randomElement($autoPartNames);

        // تحديد الأسعار بناءً على نوع القطعة
        $priceRanges = [
            'فلتر هواء' => [50, 200],
            'فلتر زيت' => [30, 150],
            'فلتر وقود' => [80, 300],
            'فلتر مكيف' => [100, 250],
            'فحمات فرامل أمامية' => [200, 800],
            'فحمات فرامل خلفية' => [150, 600],
            'أقراص فرامل' => [300, 1200],
            'زيت محرك' => [150, 500],
            'زيت فتيس' => [200, 600],
            'سائل فرامل' => [50, 150],
            'سائل مكيف' => [80, 200],
            'بطارية سيارة' => [800, 2500],
            'شمعات إشعال' => [40, 200],
            'كويلات إشعال' => [150, 600],
            'مصابيح أمامية' => [300, 1500],
            'مصابيح خلفية' => [200, 800],
            'إطارات' => [800, 3000],
            'ممتص صدمات' => [400, 1500],
            'رديتر' => [600, 2000],
            'مروحة رديتر' => [300, 800],
            'حساس أكسجين' => [200, 600],
            'حساس حرارة' => [100, 300],
            'طرمبة مياه' => [300, 1000],
            'طرمبة وقود' => [400, 1200],
            'سير مولد' => [80, 250],
            'سير تكييف' => [100, 300],
            'كلتش' => [800, 2500],
            'فلاي ويل' => [600, 2000],
            'جوان رأس' => [200, 800],
            'جوان كارتير' => [150, 500],
            'مرآة جانبية' => [200, 800],
            'مقبض باب' => [100, 400],
            'زجاج أمامي' => [500, 2000],
            'زجاج خلفي' => [400, 1500],
            'كشافات ضباب' => [150, 600],
            'إشارات جانبية' => [80, 300],
        ];

        $priceRange = $priceRanges[$partName] ?? [100, 500];
        $originalPrice = fake()->randomFloat(2, $priceRange[0], $priceRange[1]);
        $sellingPrice = $originalPrice * fake()->randomFloat(2, 0.8, 0.95); // خصم 5-20%

        // أوصاف واقعية لقطع الغيار
        $descriptions = [
            'فلتر هواء' => 'فلتر هواء عالي الجودة يحمي المحرك من الأتربة والشوائب',
            'فلتر زيت' => 'فلتر زيت أصلي يضمن نظافة زيت المحرك وإطالة عمره',
            'فحمات فرامل أمامية' => 'فحمات فرامل أمامية عالية الأداء توفر أقصى قوة توقف',
            'بطارية سيارة' => 'بطارية سيارة بقوة عالية وعمر افتراضي طويل',
            'إطارات' => 'إطارات عالية الجودة توفر قبضة ممتازة على الطريق',
        ];

        $shortDesc = $descriptions[$partName] ?? "قطعة غيار أصلية عالية الجودة لسيارتك";

        return [
            'category_id' => Category::inRandomOrder()->first()?->id ?? Category::factory(),
            'brand_id' => Brand::inRandomOrder()->first()?->id ?? Brand::factory(),
            'name' => [
                'en' => $this->translateToEnglish($partName),
                'ar' => $partName
            ],
            'slug' => Str::slug($this->translateToEnglish($partName) . '-' . fake()->randomNumber(4)),
            'short_description' => [
                'en' => $this->translateDescriptionToEnglish($shortDesc),
                'ar' => $shortDesc
            ],
            'description' => [
                'en' => $this->generateEnglishDescription($partName),
                'ar' => $this->generateArabicDescription($partName)
            ],
            'price' => $originalPrice,
            'selling_price' => $sellingPrice,
            'qty' => fake()->numberBetween(5, 100),
            'minqty' => fake()->numberBetween(1, 5),
            'tax' => 14.00, // ضريبة القيمة المضافة في مصر
            'status' => fake()->boolean(85), // 85% من المنتجات نشطة
            'trend' => fake()->boolean(20), // 20% من المنتجات رائجة
            'meta_title' => [
                'en' => $this->translateToEnglish($partName) . ' - High Quality Auto Parts',
                'ar' => $partName . ' - قطع غيار عالية الجودة'
            ],
            'meta_keywords' => [
                'en' => $this->generateEnglishKeywords($partName),
                'ar' => $this->generateArabicKeywords($partName)
            ],
            'meta_description' => [
                'en' => 'Buy high quality ' . $this->translateToEnglish($partName) . ' for your car. Original and aftermarket parts available.',
                'ar' => 'اشتري ' . $partName . ' عالي الجودة لسيارتك. قطع غيار أصلية وبديلة متوفرة.'
            ],
        ];
    }

    /**
     * ترجمة أسماء قطع الغيار إلى الإنجليزية
     */
    private function translateToEnglish($arabicName): string
    {
        $translations = [
            'فلتر هواء' => 'Air Filter',
            'فلتر زيت' => 'Oil Filter',
            'فلتر وقود' => 'Fuel Filter',
            'فلتر مكيف' => 'Cabin Filter',
            'فحمات فرامل أمامية' => 'Front Brake Pads',
            'فحمات فرامل خلفية' => 'Rear Brake Pads',
            'أقراص فرامل' => 'Brake Discs',
            'زيت محرك' => 'Engine Oil',
            'زيت فتيس' => 'Transmission Oil',
            'سائل فرامل' => 'Brake Fluid',
            'سائل مكيف' => 'AC Refrigerant',
            'بطارية سيارة' => 'Car Battery',
            'شمعات إشعال' => 'Spark Plugs',
            'كويلات إشعال' => 'Ignition Coils',
            'مصابيح أمامية' => 'Headlights',
            'مصابيح خلفية' => 'Tail Lights',
            'إطارات' => 'Tires',
            'ممتص صدمات' => 'Shock Absorbers',
            'رديتر' => 'Radiator',
            'مروحة رديتر' => 'Radiator Fan',
            'حساس أكسجين' => 'Oxygen Sensor',
            'حساس حرارة' => 'Temperature Sensor',
            'طرمبة مياه' => 'Water Pump',
            'طرمبة وقود' => 'Fuel Pump',
            'سير مولد' => 'Alternator Belt',
            'سير تكييف' => 'AC Belt',
            'كلتش' => 'Clutch',
            'فلاي ويل' => 'Flywheel',
            'جوان رأس' => 'Head Gasket',
            'جوان كارتير' => 'Oil Pan Gasket',
            'مرآة جانبية' => 'Side Mirror',
            'مقبض باب' => 'Door Handle',
            'زجاج أمامي' => 'Windshield',
            'زجاج خلفي' => 'Rear Glass',
            'كشافات ضباب' => 'Fog Lights',
            'إشارات جانبية' => 'Turn Signals',
        ];

        return $translations[$arabicName] ?? 'Auto Part';
    }

    /**
     * ترجمة الأوصاف إلى الإنجليزية
     */
    private function translateDescriptionToEnglish($arabicDesc): string
    {
        $translations = [
            'فلتر هواء عالي الجودة يحمي المحرك من الأتربة والشوائب' => 'High quality air filter that protects the engine from dust and debris',
            'فلتر زيت أصلي يضمن نظافة زيت المحرك وإطالة عمره' => 'Original oil filter ensures engine oil cleanliness and extends its life',
            'فحمات فرامل أمامية عالية الأداء توفر أقصى قوة توقف' => 'High performance front brake pads provide maximum stopping power',
            'بطارية سيارة بقوة عالية وعمر افتراضي طويل' => 'High power car battery with long lifespan',
            'إطارات عالية الجودة توفر قبضة ممتازة على الطريق' => 'High quality tires provide excellent road grip',
            'قطعة غيار أصلية عالية الجودة لسيارتك' => 'Original high quality auto part for your car',
        ];

        return $translations[$arabicDesc] ?? 'High quality original auto part for your vehicle';
    }

    /**
     * إنشاء وصف مفصل باللغة الإنجليزية
     */
    private function generateEnglishDescription($partName): string
    {
        $englishName = $this->translateToEnglish($partName);

        $descriptions = [
            'Air Filter' => 'Premium air filter designed to protect your engine from harmful particles and contaminants. Made with high-quality materials for optimal filtration and long-lasting performance. Compatible with various car models.',
            'Oil Filter' => 'High-performance oil filter that ensures clean oil circulation throughout your engine. Features advanced filtration technology to remove impurities and extend engine life.',
            'Brake Pads' => 'Professional-grade brake pads engineered for superior stopping power and safety. Made with premium friction materials for consistent performance in all driving conditions.',
            'Car Battery' => 'Reliable car battery with high cranking power and long service life. Features maintenance-free design and excellent performance in extreme weather conditions.',
            'Tires' => 'Premium tires designed for optimal performance, safety, and durability. Features advanced tread pattern for excellent grip and handling in various road conditions.',
        ];

        return $descriptions[$englishName] ?? "High-quality {$englishName} designed for optimal performance and reliability. Made with premium materials and engineered to meet or exceed OEM specifications. Perfect replacement part for your vehicle.";
    }

    /**
     * إنشاء وصف مفصل باللغة العربية
     */
    private function generateArabicDescription($partName): string
    {
        $descriptions = [
            'فلتر هواء' => 'فلتر هواء متميز مصمم لحماية محركك من الجسيمات الضارة والملوثات. مصنوع من مواد عالية الجودة للحصول على أفضل ترشيح وأداء طويل الأمد. متوافق مع موديلات سيارات مختلفة.',
            'فلتر زيت' => 'فلتر زيت عالي الأداء يضمن دوران الزيت النظيف في جميع أنحاء المحرك. يتميز بتقنية ترشيح متقدمة لإزالة الشوائب وإطالة عمر المحرك.',
            'فحمات فرامل أمامية' => 'فحمات فرامل احترافية مصممة لقوة توقف فائقة وأمان عالي. مصنوعة من مواد احتكاك متميزة للحصول على أداء ثابت في جميع ظروف القيادة.',
            'بطارية سيارة' => 'بطارية سيارة موثوقة بقوة تشغيل عالية وعمر خدمة طويل. تتميز بتصميم خالي من الصيانة وأداء ممتاز في الظروف الجوية القاسية.',
            'إطارات' => 'إطارات متميزة مصممة للأداء الأمثل والأمان والمتانة. تتميز بنمط مداس متقدم للحصول على قبضة ممتازة والتحكم في ظروف الطريق المختلفة.',
        ];

        return $descriptions[$partName] ?? "{$partName} عالي الجودة مصمم للأداء الأمثل والموثوقية. مصنوع من مواد متميزة ومهندس لتلبية أو تجاوز مواصفات الشركة المصنعة الأصلية. قطعة استبدال مثالية لسيارتك.";
    }

    /**
     * إنشاء كلمات مفتاحية باللغة الإنجليزية
     */
    private function generateEnglishKeywords($partName): string
    {
        $englishName = $this->translateToEnglish($partName);
        return strtolower($englishName) . ', auto parts, car parts, original parts, aftermarket, replacement, automotive, spare parts';
    }

    /**
     * إنشاء كلمات مفتاحية باللغة العربية
     */
    private function generateArabicKeywords($partName): string
    {
        return $partName . ', قطع غيار, قطع سيارات, قطع أصلية, قطع بديلة, استبدال, سيارات, قطع احتياطية';
    }

    /**
     * إنشاء منتج رائج
     */
    public function trending(): static
    {
        return $this->state(fn (array $attributes) => [
            'trend' => 1,
            'status' => 1,
        ]);
    }

    /**
     * إنشاء منتج غير نشط
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 0,
        ]);
    }

    /**
     * إنشاء منتج بسعر مخفض
     */
    public function onSale(): static
    {
        return $this->state(function (array $attributes) {
            $originalPrice = $attributes['price'] ?? fake()->randomFloat(2, 100, 1000);
            $discountedPrice = $originalPrice * fake()->randomFloat(2, 0.6, 0.8); // خصم 20-40%

            return [
                'price' => $originalPrice,
                'selling_price' => $discountedPrice,
                'trend' => 1, // المنتجات المخفضة عادة رائجة
            ];
        });
    }

    /**
     * إنشاء منتج بكمية قليلة (نفاد الكمية قريباً)
     */
    public function lowStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'qty' => fake()->numberBetween(1, 5),
            'minqty' => 5,
        ]);
    }

    /**
     * إنشاء منتج نفدت كميته
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'qty' => 0,
            'status' => 0, // غير نشط لأنه نفد
        ]);
    }

    /**
     * إنشاء منتج بسعر مرتفع (قطع غيار فاخرة)
     */
    public function premium(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'price' => fake()->randomFloat(2, 1000, 5000),
                'selling_price' => fake()->randomFloat(2, 800, 4500),
                'trend' => fake()->boolean(30), // 30% احتمال أن تكون رائجة
            ];
        });
    }

    /**
     * إنشاء منتج بسعر اقتصادي
     */
    public function budget(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'price' => fake()->randomFloat(2, 20, 200),
                'selling_price' => fake()->randomFloat(2, 15, 180),
            ];
        });
    }
}

