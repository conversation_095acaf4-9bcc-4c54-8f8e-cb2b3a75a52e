<?php $__env->startSection('TitlePage', 'Catalogs'); ?>
<?php $__env->startSection('content'); ?>
<!-- Breadcrumb Start -->
<div class="container-fluid">
    <div class="row px-xl-5">
        <div class="col-12">
            <nav class="breadcrumb bg-light mb-30">
                <a class="breadcrumb-item text-dark" href="<?php echo e(route('home')); ?>"><?php echo e(__('breadcrumb.home')); ?></a>
                <span class="breadcrumb-item active"><?php echo e(__('breadcrumb.catalogs')); ?></span>
            </nav>
        </div>
    </div>
</div>
<!-- Breadcrumb End -->

<!-- Catalogs Start -->
<div class="container-fluid">
    <div class="row px-xl-5">
        <div class="col-12">
            <div class="section-title position-relative text-center mx-xl-5 mb-4">
                <h2 class="font-weight-bold"><span class="bg-secondary pr-3 ps-3"><?php echo e(__('home.catalogs')); ?></span></h2>
                <p class="text-muted"><?php echo e(__('home.catalogs_description')); ?></p>
            </div>
        </div>
    </div>

    <div class="row px-xl-5">
        <?php if($catalogs->count() > 0): ?>
            <?php $__currentLoopData = $catalogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $catalog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-4 col-md-6 col-sm-12 pb-1" data-aos="fade-up" data-aos-delay="<?php echo e($loop->iteration * 100); ?>">
                <div class="card product-item border-0 mb-4">
                    <div class="card-header product-img position-relative overflow-hidden bg-transparent border p-0">
                        <img class="img-fluid w-100" src="<?php echo e(Storage::url($catalog->cover_image)); ?>" alt="<?php echo e($catalog->title_en); ?>">
                    </div>
                    <div class="card-body border-left border-right text-center p-0 pt-4 pb-3">
                        <h6 class="text-truncate mb-3"><?php echo e(app()->getLocale() == 'en' ? $catalog->title_en : $catalog->title_ar); ?></h6>
                        <p class="text-muted"><?php echo e(app()->getLocale() == 'en' ? $catalog->description_en : $catalog->description_ar); ?></p>
                        <div class="d-flex justify-content-center">
                            <span class="badge <?php echo e($catalog->file_type == 'pdf' ? 'bg-danger' : 'bg-success'); ?> text-white">
                                <?php echo e(strtoupper($catalog->file_type)); ?>

                            </span>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-between bg-light border">
                        <a href="<?php echo e(route('website.catalog.download', $catalog->id)); ?>" class="btn btn-sm text-dark p-0">
                            <i class="fas fa-download text-primary mr-1"></i><?php echo e(__('home.download')); ?>

                        </a>
                        <a href="<?php echo e(route('website.catalog.download', $catalog->id)); ?>" class="btn btn-sm text-dark p-0">
                            <i class="fas fa-eye text-primary mr-1"></i><?php echo e(__('home.view')); ?>

                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
            <div class="col-12 text-center py-5">
                <div class="alert alert-info">
                    <h4><?php echo e(__('home.no_catalogs')); ?></h4>
                    <p><?php echo e(__('home.check_back_later')); ?></p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- Catalogs End -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('website.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/website/catalogs.blade.php ENDPATH**/ ?>