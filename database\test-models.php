<?php

/**
 * اختبار سريع للنماذج المحدثة
 * 
 * لتشغيل هذا الاختبار:
 * php artisan tinker
 * ثم انسخ والصق الكود أدناه
 */

echo "🧪 اختبار النماذج المحدثة...\n\n";

// 1. اختبار Category Model
echo "📂 اختبار Category Model...\n";
try {
    $category = new \App\Models\Category();
    echo "✅ Category Model يعمل بشكل صحيح\n";
    echo "   - HasTranslations: " . (in_array('Spatie\Translatable\HasTranslations', class_uses($category)) ? 'نعم' : 'لا') . "\n";
    echo "   - Translatable fields: " . implode(', ', $category->translatable) . "\n";
} catch (Exception $e) {
    echo "❌ خطأ في Category Model: " . $e->getMessage() . "\n";
}

// 2. اختبار Product Model
echo "\n📦 اختبار Product Model...\n";
try {
    $product = new \App\Models\Product();
    echo "✅ Product Model يعمل بشكل صحيح\n";
    echo "   - HasTranslations: " . (in_array('Spatie\Translatable\HasTranslations', class_uses($product)) ? 'نعم' : 'لا') . "\n";
    echo "   - Translatable fields: " . implode(', ', $product->translatable) . "\n";
} catch (Exception $e) {
    echo "❌ خطأ في Product Model: " . $e->getMessage() . "\n";
}

// 3. اختبار SiteSetting Model
echo "\n⚙️ اختبار SiteSetting Model...\n";
try {
    $siteSetting = new \App\Models\SiteSetting();
    echo "✅ SiteSetting Model يعمل بشكل صحيح\n";
    echo "   - HasTranslations: " . (in_array('Spatie\Translatable\HasTranslations', class_uses($siteSetting)) ? 'نعم' : 'لا') . "\n";
    echo "   - Translatable fields: " . implode(', ', $siteSetting->translatable) . "\n";
} catch (Exception $e) {
    echo "❌ خطأ في SiteSetting Model: " . $e->getMessage() . "\n";
}

// 4. اختبار Brand Model
echo "\n🏷️ اختبار Brand Model...\n";
try {
    $brand = new \App\Models\Brand();
    echo "✅ Brand Model يعمل بشكل صحيح\n";
} catch (Exception $e) {
    echo "❌ خطأ في Brand Model: " . $e->getMessage() . "\n";
}

// 5. اختبار User Model
echo "\n👥 اختبار User Model...\n";
try {
    $user = new \App\Models\User();
    echo "✅ User Model يعمل بشكل صحيح\n";
} catch (Exception $e) {
    echo "❌ خطأ في User Model: " . $e->getMessage() . "\n";
}

echo "\n🎉 جميع النماذج تعمل بشكل صحيح!\n";
echo "الآن يمكنك تشغيل السيدرز بأمان:\n";
echo "php artisan migrate:fresh --seed\n";

/**
 * للتشغيل:
 * 
 * 1. اختبار النماذج:
 *    php artisan tinker
 *    ثم انسخ والصق الكود أعلاه
 * 
 * 2. تشغيل السيدرز:
 *    php artisan migrate:fresh --seed
 */
