<?php

namespace Database\Factories;

use App\Models\DiscountCoupon;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DiscountCoupon>
 */
class DiscountCouponFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $discountType = fake()->randomElement(['fixed', 'percentage']);
        $discountAmount = $discountType === 'percentage' 
            ? fake()->numberBetween(5, 50) 
            : fake()->randomFloat(2, 10, 200);

        return [
            'code' => fake()->unique()->regexify('[A-Z]{4}[0-9]{2}'),
            'name' => fake()->words(3, true),
            'description' => fake()->sentence(),
            'discount_type' => $discountType,
            'discount_amount' => $discountAmount,
            'min_order_amount' => fake()->randomFloat(2, 50, 500),
            'max_uses' => fake()->optional()->numberBetween(10, 1000),
            'max_uses_user' => fake()->optional()->numberBetween(1, 5),
            'start_date' => fake()->dateTimeBetween('now', '+1 week'),
            'end_date' => fake()->dateTimeBetween('+1 week', '+3 months'),
            'is_active' => fake()->boolean(80), // 80% chance of being active
            'created_by' => User::factory(),
        ];
    }

    /**
     * Create an active coupon.
     *
     * @return $this
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => 1,
            'start_date' => now()->subDay(),
            'end_date' => now()->addMonth(),
        ]);
    }

    /**
     * Create an expired coupon.
     *
     * @return $this
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'start_date' => now()->subMonth(),
            'end_date' => now()->subDay(),
        ]);
    }

    /**
     * Create a percentage discount coupon.
     *
     * @return $this
     */
    public function percentage(): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_type' => 'percentage',
            'discount_amount' => fake()->numberBetween(5, 50),
        ]);
    }

    /**
     * Create a fixed amount discount coupon.
     *
     * @return $this
     */
    public function fixed(): static
    {
        return $this->state(fn (array $attributes) => [
            'discount_type' => 'fixed',
            'discount_amount' => fake()->randomFloat(2, 10, 200),
        ]);
    }

    /**
     * Create a limited use coupon.
     *
     * @return $this
     */
    public function limitedUse(): static
    {
        return $this->state(fn (array $attributes) => [
            'max_uses' => fake()->numberBetween(5, 50),
            'max_uses_user' => fake()->numberBetween(1, 3),
        ]);
    }
}
