<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ProductRating;
use App\Models\Product;
use App\Models\User;

class ProductRatingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get products and users
        $products = Product::take(30)->get();
        $users = User::where('type', 0)->take(20)->get(); // Get regular users

        if ($products->isEmpty()) {
            $this->command->warn('No products found. Please run ProductSeeder first.');
            return;
        }

        // Create ratings for products
        foreach ($products as $product) {
            // Each product gets 2-8 ratings
            $ratingCount = rand(2, 8);

            for ($i = 0; $i < $ratingCount; $i++) {
                $user = $users->isNotEmpty() ? $users->random() : null;
                
                ProductRating::factory()->approved()->create([
                    'product_id' => $product->id,
                    'username' => $user ? $user->name : fake()->name(),
                    'email' => $user ? $user->email : fake()->safeEmail(),
                ]);
            }
        }

        // Create some high ratings
        ProductRating::factory(15)->approved()->highRating()->create();

        // Create some low ratings
        ProductRating::factory(5)->approved()->lowRating()->create();

        // Create some pending ratings
        ProductRating::factory(10)->pending()->create();

        // Create some additional random ratings
        ProductRating::factory(25)->create();
    }
}
