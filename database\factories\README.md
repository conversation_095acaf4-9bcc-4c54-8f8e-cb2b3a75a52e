# Database Factories Documentation

This document explains how to use the factories created for the e-commerce auto parts project.

## Available Factories

### Core Factories

1. **UserFactory** - Creates users with different roles
2. **CountryFactory** - Creates countries
3. **SiteSettingFactory** - Creates site configuration
4. **CategoryFactory** - Creates product categories
5. **BrandFactory** - Creates product brands
6. **ProductFactory** - Creates products
7. **ProductImageFactory** - Creates product images

### E-commerce Factories

8. **CartFactory** - Creates shopping cart items
9. **WishlistFactory** - Creates wishlist items
10. **OrderFactory** - Creates orders
11. **OrderItemFactory** - Creates order items
12. **DiscountCouponFactory** - Creates discount coupons
13. **ShippingChargesFactory** - Creates shipping charges
14. **ProductRatingFactory** - Creates product reviews/ratings
15. **CustomerAddressFactory** - Creates customer addresses

## Usage Examples

### Basic Usage

```php
// Create a single user
$user = User::factory()->create();

// Create 10 users
$users = User::factory(10)->create();

// Create user with specific data
$admin = User::factory()->create([
    'name' => 'Admin User',
    'email' => '<EMAIL>'
]);
```

### Using States

```php
// Create admin user
$admin = User::factory()->admin()->create();

// Create inactive user
$inactiveUser = User::factory()->inactive()->create();

// Create unverified user
$unverifiedUser = User::factory()->unverified()->create();

// Create Egyptian country
$egypt = Country::factory()->egypt()->create();

// Create active discount coupon
$coupon = DiscountCoupon::factory()->active()->percentage()->create();

// Create high-rated product review
$rating = ProductRating::factory()->approved()->highRating()->create();
```

### Complex Relationships

```php
// Create user with cart items
$user = User::factory()->create();
$products = Product::factory(3)->create();

foreach ($products as $product) {
    Cart::factory()->create([
        'user_id' => $user->id,
        'product_id' => $product->id,
        'name' => $product->name,
        'selling_price' => $product->selling_price
    ]);
}

// Create order with items
$order = Order::factory()->create();
$products = Product::factory(2)->create();

foreach ($products as $product) {
    OrderItem::factory()->create([
        'order_id' => $order->id,
        'product_id' => $product->id,
        'name' => $product->name,
        'price' => $product->selling_price
    ]);
}
```

## Factory States

### UserFactory States
- `admin()` - Creates admin user (type = 1)
- `inactive()` - Creates inactive user (status = 0)
- `unverified()` - Creates user with null email_verified_at

### CountryFactory States
- `egypt()` - Creates Egypt country
- `usa()` - Creates USA country

### SiteSettingFactory States
- `arabic()` - Creates Arabic-focused site settings

### OrderFactory States
- `pending()` - Creates pending order
- `paid()` - Creates paid order
- `delivered()` - Creates delivered order
- `cancelled()` - Creates cancelled order

### DiscountCouponFactory States
- `active()` - Creates active coupon
- `expired()` - Creates expired coupon
- `percentage()` - Creates percentage discount
- `fixed()` - Creates fixed amount discount
- `limitedUse()` - Creates limited use coupon

### ProductRatingFactory States
- `approved()` - Creates approved rating (status = 1)
- `pending()` - Creates pending rating (status = 0)
- `highRating()` - Creates 4-5 star rating
- `lowRating()` - Creates 1-2 star rating

### ShippingChargesFactory States
- `egypt()` - Creates shipping for Egypt
- `usa()` - Creates shipping for USA
- `free()` - Creates free shipping (amount = 0)
- `expensive()` - Creates expensive shipping

## Running Seeders

To populate your database with test data:

```bash
# Run all seeders
php artisan db:seed

# Run specific seeder
php artisan db:seed --class=UserSeeder
php artisan db:seed --class=ProductSeeder
php artisan db:seed --class=OrderSeeder

# Fresh migration with seeding
php artisan migrate:fresh --seed
```

## Testing Usage

```php
// In your tests
public function test_user_can_add_to_cart()
{
    $user = User::factory()->create();
    $product = Product::factory()->create();
    
    // Test cart functionality
    $response = $this->actingAs($user)
        ->post('/cart/add', [
            'product_id' => $product->id,
            'qty' => 2
        ]);
    
    $this->assertDatabaseHas('carts', [
        'user_id' => $user->id,
        'product_id' => $product->id
    ]);
}
```

## Tips

1. Always create dependencies first (Country before User, Category/Brand before Product)
2. Use states for specific scenarios in tests
3. Use `create()` when you need the model persisted to database
4. Use `make()` when you only need the model instance without saving
5. Combine multiple states: `User::factory()->admin()->unverified()->create()`
