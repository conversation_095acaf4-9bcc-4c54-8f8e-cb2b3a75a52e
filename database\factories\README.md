# Database Factories Documentation

This document explains how to use the factories created for the e-commerce auto parts project.

## Available Factories

### Core Factories

1. **UserFactory** - Creates users with different roles
2. **CountryFactory** - Creates countries
3. **SiteSettingFactory** - Creates site configuration
4. **CategoryFactory** - Creates product categories
5. **BrandFactory** - Creates product brands
6. **ProductFactory** - Creates products
7. **ProductImageFactory** - Creates product images

### E-commerce Factories

8. **CartFactory** - Creates shopping cart items
9. **WishlistFactory** - Creates wishlist items
10. **OrderFactory** - Creates orders
11. **OrderItemFactory** - Creates order items
12. **DiscountCouponFactory** - Creates discount coupons
13. **ShippingChargesFactory** - Creates shipping charges
14. **ProductRatingFactory** - Creates product reviews/ratings
15. **CustomerAddressFactory** - Creates customer addresses

## Usage Examples

### Basic Usage

```php
// Create a single user
$user = User::factory()->create();

// Create 10 users
$users = User::factory(10)->create();

// Create user with specific data
$admin = User::factory()->create([
    'name' => 'Admin User',
    'email' => '<EMAIL>'
]);
```

### Using States

```php
// Create admin user
$admin = User::factory()->admin()->create();

// Create inactive user
$inactiveUser = User::factory()->inactive()->create();

// Create unverified user
$unverifiedUser = User::factory()->unverified()->create();

// Create Egyptian country
$egypt = Country::factory()->egypt()->create();

// Create active discount coupon
$coupon = DiscountCoupon::factory()->active()->percentage()->create();

// Create high-rated product review
$rating = ProductRating::factory()->approved()->highRating()->create();

// Create trending auto parts products
$trendingProducts = Product::factory(5)->trending()->create();

// Create discounted products
$saleProducts = Product::factory(3)->onSale()->create();

// Create premium auto parts
$premiumParts = Product::factory(2)->premium()->create();

// Create budget-friendly parts
$budgetParts = Product::factory(10)->budget()->create();

// Create low stock products
$lowStockProducts = Product::factory(3)->lowStock()->create();
```

### Complex Relationships

```php
// Create user with cart items
$user = User::factory()->create();
$products = Product::factory(3)->create();

foreach ($products as $product) {
    Cart::factory()->create([
        'user_id' => $user->id,
        'product_id' => $product->id,
        'name' => $product->name,
        'selling_price' => $product->selling_price
    ]);
}

// Create order with items
$order = Order::factory()->create();
$products = Product::factory(2)->create();

foreach ($products as $product) {
    OrderItem::factory()->create([
        'order_id' => $order->id,
        'product_id' => $product->id,
        'name' => $product->name,
        'price' => $product->selling_price
    ]);
}
```

## Factory States

### UserFactory States
- `admin()` - Creates admin user (type = 1)
- `inactive()` - Creates inactive user (status = 0)
- `unverified()` - Creates user with null email_verified_at

### CountryFactory States
- `egypt()` - Creates Egypt country
- `usa()` - Creates USA country

### SiteSettingFactory States
- `arabic()` - Creates Arabic-focused site settings

### OrderFactory States
- `pending()` - Creates pending order
- `paid()` - Creates paid order
- `delivered()` - Creates delivered order
- `cancelled()` - Creates cancelled order

### DiscountCouponFactory States
- `active()` - Creates active coupon
- `expired()` - Creates expired coupon
- `percentage()` - Creates percentage discount
- `fixed()` - Creates fixed amount discount
- `limitedUse()` - Creates limited use coupon

### ProductFactory States
- `trending()` - Creates trending product (trend = 1, status = 1)
- `inactive()` - Creates inactive product (status = 0)
- `onSale()` - Creates discounted product (20-40% off)
- `lowStock()` - Creates low stock product (1-5 qty)
- `outOfStock()` - Creates out of stock product (qty = 0)
- `premium()` - Creates premium product (1000-5000 EGP)
- `budget()` - Creates budget product (20-200 EGP)

### ProductRatingFactory States
- `approved()` - Creates approved rating (status = 1)
- `pending()` - Creates pending rating (status = 0)
- `highRating()` - Creates 4-5 star rating
- `lowRating()` - Creates 1-2 star rating

### ShippingChargesFactory States
- `egypt()` - Creates shipping for Egypt
- `usa()` - Creates shipping for USA
- `free()` - Creates free shipping (amount = 0)
- `expensive()` - Creates expensive shipping

## Running Seeders

To populate your database with test data:

```bash
# Run all seeders
php artisan db:seed

# Run specific seeder
php artisan db:seed --class=UserSeeder
php artisan db:seed --class=ProductSeeder
php artisan db:seed --class=OrderSeeder

# Fresh migration with seeding
php artisan migrate:fresh --seed
```

## Testing Usage

```php
// In your tests
public function test_user_can_add_to_cart()
{
    $user = User::factory()->create();
    $product = Product::factory()->create();

    // Test cart functionality
    $response = $this->actingAs($user)
        ->post('/cart/add', [
            'product_id' => $product->id,
            'qty' => 2
        ]);

    $this->assertDatabaseHas('carts', [
        'user_id' => $user->id,
        'product_id' => $product->id
    ]);
}
```

## Auto Parts Specific Features

### Realistic Auto Parts Products

The ProductFactory now generates realistic auto parts with:

- **36 different auto parts types** in Arabic and English
- **Realistic pricing** based on actual auto parts market prices in Egypt
- **Proper descriptions** in both Arabic and English
- **SEO-optimized meta data** for both languages
- **Logical price ranges** for each part type

### Auto Parts Categories

The factory includes these auto parts:

**Filters & Fluids:**
- فلتر هواء (Air Filter): 50-200 EGP
- فلتر زيت (Oil Filter): 30-150 EGP
- زيت محرك (Engine Oil): 150-500 EGP

**Brake System:**
- فحمات فرامل (Brake Pads): 150-800 EGP
- أقراص فرامل (Brake Discs): 300-1200 EGP

**Electrical:**
- بطارية سيارة (Car Battery): 800-2500 EGP
- شمعات إشعال (Spark Plugs): 40-200 EGP

**Body & Glass:**
- زجاج أمامي (Windshield): 500-2000 EGP
- مرآة جانبية (Side Mirror): 200-800 EGP

### Example Usage for Auto Parts

```php
// Create specific auto parts
$airFilter = Product::factory()->budget()->create(); // Cheap air filter
$premiumTires = Product::factory()->premium()->create(); // Expensive tires
$saleItems = Product::factory(5)->onSale()->create(); // Sale items

// Create realistic auto parts store
Product::factory(20)->create(); // Regular parts
Product::factory(5)->trending()->create(); // Popular parts
Product::factory(3)->lowStock()->create(); // Almost sold out
```

## Tips

1. Always create dependencies first (Country before User, Category/Brand before Product)
2. Use states for specific scenarios in tests
3. Use `create()` when you need the model persisted to database
4. Use `make()` when you only need the model instance without saving
5. Combine multiple states: `User::factory()->admin()->unverified()->create()`
6. **Auto parts pricing is in Egyptian Pounds (EGP)** and reflects real market prices
7. **All products include Arabic and English translations** for multilingual support
