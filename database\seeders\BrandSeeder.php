<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء 6 علامات تجارية واقعية لقطع غيار السيارات

        // العلامات التجارية الرئيسية (نشطة)
        \App\Models\Brand::factory()->active()->create([
            'name' => 'Bosch',
            'slug' => 'bosch',
            'image' => 'brands/bosch.jpg'
        ]);

        \App\Models\Brand::factory()->active()->create([
            'name' => 'NGK',
            'slug' => 'ngk',
            'image' => 'brands/ngk.jpg'
        ]);

        \App\Models\Brand::factory()->active()->create([
            'name' => 'Brembo',
            'slug' => 'brembo',
            'image' => 'brands/brembo.jpg'
        ]);

        \App\Models\Brand::factory()->active()->create([
            'name' => 'Mann Filter',
            'slug' => 'mann-filter',
            'image' => 'brands/mann-filter.jpg'
        ]);

        \App\Models\Brand::factory()->active()->create([
            'name' => 'Valeo',
            'slug' => 'valeo',
            'image' => 'brands/valeo.jpg'
        ]);

        \App\Models\Brand::factory()->active()->create([
            'name' => 'Mahle',
            'slug' => 'mahle',
            'image' => 'brands/mahle.jpg'
        ]);
    }
}
