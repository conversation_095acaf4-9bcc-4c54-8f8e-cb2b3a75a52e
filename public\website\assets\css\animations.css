/* Animations and Enhanced Styles for Auto Parts Website */

/* General Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
  overflow: hidden;
}

.slide-in-left {
  animation: slideInLeft 1s ease-in-out;
  overflow: hidden;
}

.slide-in-right {
  animation: slideInRight 1s ease-in-out;
  overflow: hidden;
}

.zoom-in {
  animation: zoomIn 0.8s ease-in-out;
  overflow: hidden;
}

.bounce {
  animation: bounce 1s ease-in-out;
  overflow: hidden;
}

/* Keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-30px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(30px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes zoomIn {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* Enhanced Carousel */
.carousel-item {
  position: relative;
}

.carousel-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0,0,0,0.7), rgba(0,0,0,0.3));
  z-index: 1;
}

.carousel-caption {
  z-index: 2;
}

.carousel-caption h1 {
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.carousel-caption p {
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* Enhanced Feature Boxes */
.feature-box {
  transition: all 0.3s ease;
  overflow: hidden;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.feature-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.feature-icon {
  transition: all 0.3s ease;
}

.feature-box:hover .feature-icon {
  transform: scale(1.2);
}

/* Enhanced Category Cards */
.cat-item {
  transition: all 0.3s ease;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.cat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.cat-item img {
  transition: all 0.5s ease;
}

.cat-item:hover img {
  transform: scale(1.1);
}

/* Enhanced Product Cards - Moved to inline styles in trend-products.blade.php */

/* Enhanced Vendor Section */
.vendor-carousel .bg-light {
  transition: all 0.3s ease;
  border-radius: 5px;
  overflow: hidden;
}

.vendor-carousel .bg-light:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.vendor-carousel img {
  transition: all 0.3s ease;
  filter: grayscale(100%);
  opacity: 0.7;
}

.vendor-carousel .bg-light:hover img {
  filter: grayscale(0%);
  opacity: 1;
}

/* Hero Banner */
.hero-banner {
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('../img/pexels-mikebirdy-190574.jpg');
  background-size: cover;
  background-position: center;
  padding: 60px 0;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.hero-banner-content {
  text-align: center;
  color: #fff;
  max-width: 100%;
  padding: 0 15px;
}

.hero-banner-content h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-banner-content p {
  font-size: 1.1rem;
  margin-bottom: 30px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Promo Banners */
.promo-banner {
  position: relative;
  overflow: hidden;
  border-radius: 5px;
  max-width: 100%;
}

.promo-banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.4));
  z-index: 1;
}

.promo-banner-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 15px;
  z-index: 2;
  overflow: hidden;
}

/* Button Animations */
.btn-animated {
  position: relative;
  overflow: hidden;
}

.btn-animated:after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.btn-animated:hover:after {
  left: 100%;
}

/* Scroll Animation */
.scroll-animation {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease;
}

.scroll-animation.active {
  opacity: 1;
  transform: translateY(0);
}

/* Floating Animation */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Pulse Animation */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Enhanced Section Titles */
.section-title {
  position: relative;
  margin-bottom: 30px;
}

.section-title span {
  position: relative;
  z-index: 1;
}

.section-title::after {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  height: 2px;
  background-color: #e5e5e5;
  z-index: 0;
}

/* Star Rating Enhancement */
.back-stars {
  color: #f0f0f0;
}

.front-stars {
  color: #FFD333;
}
