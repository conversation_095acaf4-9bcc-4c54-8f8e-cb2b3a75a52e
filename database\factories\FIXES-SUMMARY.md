# إصلاحات OrderFactory - ملخص التحديثات

## المشكلة الأصلية
```
SQLSTATE[42S22]: Column not found: 1054 Unknown column 'coupon_code_id' in 'field list'
```

## الإصلاحات المطبقة

### ✅ 1. إزالة العمود غير الموجود
- **المشكلة**: العمود `coupon_code_id` غير موجود في جدول `orders`
- **الحل**: تم إزالة `'coupon_code_id' => null,` من OrderFactory

### ✅ 2. تصحيح قيم حالة الدفع
- **المشكلة**: استخدام قيم خاطئة لحالة الدفع
- **الحل**: تغيير من `'failed'` إلى `'not paid'` لتتطابق مع enum في قاعدة البيانات

**قبل الإصلاح:**
```php
'payment_status' => fake()->randomElement(['pending', 'paid', 'failed']),
```

**بعد الإصلاح:**
```php
'payment_status' => fake()->randomElement(['paid', 'not paid']),
```

### ✅ 3. تصحيح قيم حالة الطلب
- **المشكلة**: استخدام `'cancelled'` غير موجود في enum
- **الحل**: إزالة `'cancelled'` واستخدام القيم المسموحة فقط

**القيم المسموحة:**
- `'pending'` - معلق
- `'shipped'` - مشحون  
- `'delivered'` - مسلم

### ✅ 4. إضافة العمود المفقود
- **إضافة**: `'shipped_date' => null,` للطلبات الجديدة
- **تحديث**: إضافة تاريخ شحن للطلبات المشحونة والمسلمة

### ✅ 5. تحديث حالات الطلبات (States)

#### الحالات المحدثة:
```php
// طلب معلق
public function pending(): static
{
    return $this->state(fn (array $attributes) => [
        'payment_status' => 'not paid',
        'status' => 'pending',
    ]);
}

// طلب مدفوع ومشحون
public function paid(): static
{
    return $this->state(fn (array $attributes) => [
        'payment_status' => 'paid',
        'status' => 'shipped',
        'shipped_date' => fake()->dateTimeBetween('-1 week', 'now'),
    ]);
}

// طلب مسلم
public function delivered(): static
{
    return $this->state(fn (array $attributes) => [
        'payment_status' => 'paid',
        'status' => 'delivered',
        'shipped_date' => fake()->dateTimeBetween('-2 weeks', '-3 days'),
    ]);
}

// طلب غير مدفوع
public function unpaid(): static
{
    return $this->state(fn (array $attributes) => [
        'payment_status' => 'not paid',
        'status' => 'pending',
    ]);
}
```

### ✅ 6. تحديث OrderSeeder
- **تغيير**: استبدال `cancelled()` بـ `unpaid()`
- **النتيجة**: جميع الطلبات تستخدم حالات صحيحة

## هيكل جدول الطلبات النهائي

### الأعمدة الأساسية:
- `id` - معرف الطلب
- `user_id` - معرف المستخدم
- `subtotal` - المجموع الفرعي
- `shipping` - رسوم الشحن
- `coupon_code` - كود الخصم (اختياري)
- `discount` - قيمة الخصم
- `grand_total` - المجموع الكلي

### حالات الطلب:
- `payment_status` - حالة الدفع: `['paid', 'not paid']`
- `status` - حالة الطلب: `['pending', 'shipped', 'delivered']`
- `shipped_date` - تاريخ الشحن (اختياري)

### معلومات العنوان:
- `name` - اسم المستلم
- `email` - البريد الإلكتروني
- `mobile` - رقم الهاتف
- `country_id` - معرف الدولة
- `address` - العنوان الأساسي
- `address2` - العنوان الثانوي (اختياري)
- `city` - المدينة
- `state` - المحافظة/الولاية
- `zip` - الرمز البريدي
- `notes` - ملاحظات (اختياري)

## الاستخدام الصحيح الآن

```php
// إنشاء طلبات مختلفة
$pendingOrders = Order::factory(5)->pending()->create();
$paidOrders = Order::factory(8)->paid()->create();
$deliveredOrders = Order::factory(3)->delivered()->create();
$unpaidOrders = Order::factory(2)->unpaid()->create();

// إنشاء طلب مع عناصر
$order = Order::factory()->create();
$products = Product::take(3)->get();

foreach ($products as $product) {
    OrderItem::factory()->create([
        'order_id' => $order->id,
        'product_id' => $product->id,
        'name' => $product->name['ar'],
        'price' => $product->selling_price,
    ]);
}
```

## اختبار الإصلاحات

لاختبار أن كل شيء يعمل بشكل صحيح:

```bash
# تشغيل المايجريشن
php artisan migrate:fresh

# تشغيل السيدرز
php artisan db:seed

# أو تشغيل OrderSeeder فقط
php artisan db:seed --class=OrderSeeder
```

## النتيجة النهائية

✅ **جميع المشاكل تم حلها**
✅ **OrderFactory يعمل بشكل صحيح**
✅ **جميع الحالات (States) تعمل كما هو متوقع**
✅ **التوافق الكامل مع هيكل قاعدة البيانات**
✅ **إنشاء طلبات واقعية مع بيانات صحيحة**
