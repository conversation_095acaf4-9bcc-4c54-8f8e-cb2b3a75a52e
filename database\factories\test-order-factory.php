<?php

/**
 * اختبار سريع لـ OrderFactory المحدث
 * 
 * لتشغيل هذا الاختبار:
 * php artisan tinker
 * ثم انسخ والصق الكود أدناه
 */

echo "🧪 اختبار OrderFactory المحدث...\n\n";

// 1. إنشاء دولة ومستخدم للاختبار
echo "📍 إنشاء البيانات الأساسية...\n";
$country = \App\Models\Country::factory()->egypt()->create();
$user = \App\Models\User::factory()->create(['country_id' => $country->id]);
echo "✅ تم إنشاء دولة ومستخدم\n\n";

// 2. اختبار إنشاء طلب عادي
echo "📦 اختبار الطلب العادي...\n";
try {
    $regularOrder = \App\Models\Order::factory()->create([
        'user_id' => $user->id,
        'country_id' => $country->id,
    ]);
    echo "✅ تم إنشاء طلب عادي بنجاح - ID: {$regularOrder->id}\n";
    echo "   - الحالة: {$regularOrder->status}\n";
    echo "   - حالة الدفع: {$regularOrder->payment_status}\n";
    echo "   - المجموع: {$regularOrder->grand_total} جنيه\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الطلب العادي: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. اختبار الطلب المعلق
echo "⏳ اختبار الطلب المعلق...\n";
try {
    $pendingOrder = \App\Models\Order::factory()->pending()->create([
        'user_id' => $user->id,
        'country_id' => $country->id,
    ]);
    echo "✅ تم إنشاء طلب معلق بنجاح - ID: {$pendingOrder->id}\n";
    echo "   - الحالة: {$pendingOrder->status}\n";
    echo "   - حالة الدفع: {$pendingOrder->payment_status}\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الطلب المعلق: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. اختبار الطلب المدفوع
echo "💰 اختبار الطلب المدفوع...\n";
try {
    $paidOrder = \App\Models\Order::factory()->paid()->create([
        'user_id' => $user->id,
        'country_id' => $country->id,
    ]);
    echo "✅ تم إنشاء طلب مدفوع بنجاح - ID: {$paidOrder->id}\n";
    echo "   - الحالة: {$paidOrder->status}\n";
    echo "   - حالة الدفع: {$paidOrder->payment_status}\n";
    echo "   - تاريخ الشحن: " . ($paidOrder->shipped_date ? $paidOrder->shipped_date : 'غير محدد') . "\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الطلب المدفوع: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. اختبار الطلب المسلم
echo "📬 اختبار الطلب المسلم...\n";
try {
    $deliveredOrder = \App\Models\Order::factory()->delivered()->create([
        'user_id' => $user->id,
        'country_id' => $country->id,
    ]);
    echo "✅ تم إنشاء طلب مسلم بنجاح - ID: {$deliveredOrder->id}\n";
    echo "   - الحالة: {$deliveredOrder->status}\n";
    echo "   - حالة الدفع: {$deliveredOrder->payment_status}\n";
    echo "   - تاريخ الشحن: " . ($deliveredOrder->shipped_date ? $deliveredOrder->shipped_date : 'غير محدد') . "\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الطلب المسلم: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. اختبار الطلب غير المدفوع
echo "💸 اختبار الطلب غير المدفوع...\n";
try {
    $unpaidOrder = \App\Models\Order::factory()->unpaid()->create([
        'user_id' => $user->id,
        'country_id' => $country->id,
    ]);
    echo "✅ تم إنشاء طلب غير مدفوع بنجاح - ID: {$unpaidOrder->id}\n";
    echo "   - الحالة: {$unpaidOrder->status}\n";
    echo "   - حالة الدفع: {$unpaidOrder->payment_status}\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الطلب غير المدفوع: " . $e->getMessage() . "\n";
}
echo "\n";

// 7. اختبار إنشاء عناصر الطلب
echo "🛍️ اختبار عناصر الطلب...\n";
try {
    // إنشاء منتجات للاختبار
    $product1 = \App\Models\Product::factory()->create();
    $product2 = \App\Models\Product::factory()->create();
    
    // إنشاء عناصر الطلب
    $orderItem1 = \App\Models\OrderItem::factory()->create([
        'order_id' => $regularOrder->id,
        'product_id' => $product1->id,
        'name' => $product1->name['ar'] ?? 'منتج تجريبي',
        'price' => $product1->selling_price,
    ]);
    
    $orderItem2 = \App\Models\OrderItem::factory()->create([
        'order_id' => $regularOrder->id,
        'product_id' => $product2->id,
        'name' => $product2->name['ar'] ?? 'منتج تجريبي 2',
        'price' => $product2->selling_price,
    ]);
    
    echo "✅ تم إنشاء عناصر الطلب بنجاح\n";
    echo "   - العنصر الأول: {$orderItem1->name} - الكمية: {$orderItem1->qty} - السعر: {$orderItem1->price}\n";
    echo "   - العنصر الثاني: {$orderItem2->name} - الكمية: {$orderItem2->qty} - السعر: {$orderItem2->price}\n";
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء عناصر الطلب: " . $e->getMessage() . "\n";
}
echo "\n";

// 8. عرض ملخص الاختبار
echo "📊 ملخص الاختبار:\n";
echo "✅ جميع أنواع الطلبات تم إنشاؤها بنجاح\n";
echo "✅ عناصر الطلبات تعمل بشكل صحيح\n";
echo "✅ العلاقات بين الجداول تعمل بشكل سليم\n";
echo "✅ حالات الطلبات (States) تعمل كما هو متوقع\n\n";

echo "🎉 OrderFactory جاهز للاستخدام!\n";

/**
 * للتشغيل:
 * 
 * 1. تأكد من تشغيل المايجريشن:
 *    php artisan migrate:fresh
 * 
 * 2. افتح Laravel Tinker:
 *    php artisan tinker
 * 
 * 3. انسخ والصق الكود أعلاه
 * 
 * أو شغل السيدرز مباشرة:
 *    php artisan db:seed --class=OrderSeeder
 */
