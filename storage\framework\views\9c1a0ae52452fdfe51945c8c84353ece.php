<div class="container-fluid bg-dark mb-30 ">
    <div class="row px-xl-5">
        <div class="col-lg-3 d-none d-lg-block">
            <a class="btn d-flex align-items-center justify-content-between bg-primary w-100" data-toggle="collapse" href="#navbar-vertical" style="height: 65px; padding: 0 30px;">
                <h6 class="text-dark m-0"><i class="fa fa-bars mr-2"></i><?php echo e(__('navbar.categories')); ?></h6>
                <i class="fa fa-angle-down text-dark"></i>
            </a>
            <nav class="collapse position-absolute navbar navbar-vertical navbar-light align-items-start p-0 bg-light" id="navbar-vertical" style="width: calc(100% - 30px); z-index: 999;">
                <div class="navbar-nav w-100">
                    <div class="nav-item dropdown dropright">
                        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">Dresses <i class="fa fa-angle-right float-right mt-1"></i></a>
                        <div class="dropdown-menu position-absolute rounded-0 border-0 m-0">
                            <a href="" class="dropdown-item">Men's Dresses</a>
                            <a href="" class="dropdown-item">Women's Dresses</a>
                            <a href="" class="dropdown-item">Baby's Dresses</a>
                        </div>
                    </div>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e(route('website.category_slug' , $category->slug)); ?>" class="nav-item nav-link"><?php echo e($category->meta_title); ?></a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </nav>
        </div>
        <div class="col-lg-9">
            <nav class="navbar navbar-expand-lg bg-dark navbar-dark py-3 py-lg-0 px-0">
                <a href="" class="text-decoration-none d-block d-lg-none">
                    <?php
                    $siteName = $site_settings->site_name ;
                    $nameParts = explode(' ', $siteName);
                    ?>

                    <?php if(count($nameParts) == 1): ?>
                        <!-- إذا كانت كلمة واحدة -->
                        <span class="h1 text-uppercase text-dark bg-light px-2"><?php echo e($nameParts[0]); ?></span>
                    <?php elseif(count($nameParts) == 2): ?>
                        <!-- إذا كانت كلمتين -->
                        <span class="h1 text-uppercase text-dark bg-light px-2"><?php echo e($nameParts[0]); ?></span>
                        <span class="h1 text-uppercase text-light bg-primary px-2 ml-n1"><?php echo e($nameParts[1]); ?></span>
                    <?php else: ?>
                        <!-- إذا كانت أكثر من كلمتين -->
                        <span class="h1 text-uppercase text-dark bg-light px-2"><?php echo e($nameParts[0]); ?></span>
                        <span class="h1 text-uppercase text-light bg-primary px-2 ml-n1"><?php echo e($nameParts[1]); ?></span>
                        <span class="h1 text-uppercase text-primary bg-dark px-2"><?php echo e(implode(' ', array_slice($nameParts, 2))); ?></span>
                    <?php endif; ?>
                </a>
                <button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbarCollapse">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse justify-content-between" id="navbarCollapse">
                    <div class="navbar-nav mr-auto py-0">
                        <a href="<?php echo e(route('home')); ?>" class="nav-item nav-link <?php echo e((Route::is('home')) ? 'active' : ''); ?>"><?php echo e(__('navbar.home')); ?> </a>
                        <a href="<?php echo e(route('website.shop')); ?>" class="nav-item nav-link <?php echo e((Route::is('website.shop')) ? 'active' : ''); ?>"><?php echo e(__('navbar.shop')); ?></a>
                        
                        <a href="<?php echo e(route('website.categories')); ?>" class="nav-item nav-link <?php echo e((Route::is('website.categories')) ? 'active' : ''); ?>"><?php echo e(__('navbar.categories')); ?></a>
                        <a href="<?php echo e(route('website.catalogs')); ?>" class="nav-item nav-link <?php echo e((Route::is('website.catalogs')) ? 'active' : ''); ?>"><?php echo e(__('navbar.catalogs')); ?></a>
                        <a href="<?php echo e(url('page/contact-us')); ?>" class="nav-item nav-link <?php echo e(Request::is('page/contact-us') ? 'active' : ''); ?>" ><?php echo e(__('navbar.contact')); ?></a>
                    </div>
                    <div class="navbar-nav ml-auto py-0 d-none d-lg-block">
                        <a href="<?php echo e(route('website.account.wishlist')); ?>" class="btn px-0">
                            <i class="fas fa-heart text-primary"></i>
                            <span id="wishlist-count" class="badge text-secondary border border-secondary rounded-circle" style="padding-bottom: 2px;">
                                <?php echo e($wishlistCount); ?>

                            </span>
                        </a>
                        <a href="<?php echo e(route('website.cart')); ?>" class="btn px-0 ml-3">
                            <i class="fas fa-shopping-cart text-primary"></i>
                            <span id="cart-count" class="badge text-secondary border border-secondary rounded-circle" style="padding-bottom: 2px;">
                                0
                            </span>
                        </a>
                    </div>

                </div>
            </nav>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/website/layouts/inc/navbar.blade.php ENDPATH**/ ?>