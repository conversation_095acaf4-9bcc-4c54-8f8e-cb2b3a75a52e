<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // تشغيل CountrySeeder أولاً
        $this->call([
            CountrySeeder::class,
        ]);

        // الحصول على مصر كدولة افتراضية
        $egypt = \App\Models\Country::where('code', 'EG')->first();

        // إنشاء مستخدم إداري
        \App\Models\User::factory()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'type' => 1, // admin
            'status' => 1,
            'country_id' => $egypt->id,
            'email_verified_at' => now(),
        ]);

        // إنشاء مستخدم عادي
        \App\Models\User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'type' => 0, // user
            'status' => 1,
            'country_id' => $egypt->id,
            'email_verified_at' => now(),
        ]);
    }
}
