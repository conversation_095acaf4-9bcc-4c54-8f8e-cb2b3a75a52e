<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🚀 بدء إنشاء البيانات الأساسية...');

        $this->call([
            // البيانات الأساسية أولاً
            CountrySeeder::class,
            SiteSettingSeeder::class,
            UserSeeder::class,

            // التصنيفات والعلامات التجارية
            CategorySeeder::class,
            BrandSeeder::class,

            // المنتجات والصور
            ProductSeeder::class,

            // باقي البيانات (اختيارية)
            // DiscountCouponSeeder::class,
            // ShippingChargesSeeder::class,
            // OrderSeeder::class,
            // CartSeeder::class,
            // WishlistSeeder::class,
            // ProductRatingSeeder::class,
        ]);

        $this->command->info('✅ تم إنشاء البيانات بنجاح!');
        $this->command->info('📊 الملخص:');
        $this->command->info('   - الدول: ' . \App\Models\Country::count());
        $this->command->info('   - المستخدمين: ' . \App\Models\User::count());
        $this->command->info('   - التصنيفات: ' . \App\Models\Category::count());
        $this->command->info('   - العلامات التجارية: ' . \App\Models\Brand::count());
        $this->command->info('   - المنتجات: ' . \App\Models\Product::count());
        $this->command->info('   - صور المنتجات: ' . \App\Models\ProductImage::count());
        $this->command->info('');
        $this->command->info('🔑 بيانات تسجيل الدخول:');
        $this->command->info('   المدير: <EMAIL> / 123456789');
        $this->command->info('   المستخدم: <EMAIL> / 123456789');
    }
}
