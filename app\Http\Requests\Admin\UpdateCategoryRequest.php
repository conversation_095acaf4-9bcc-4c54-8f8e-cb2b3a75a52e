<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
        'name'              => 'required|string|max:255',
        'slug'              => 'required',
        'description'       => 'required',
        'meta_title'        => 'required',
        'meta_description'      => 'required',
        'meta_keywords'     => 'required',
        ];
    }
}
