<?php

namespace Database\Factories;

use App\Models\ProductRating;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductRating>
 */
class ProductRatingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'username' => fake()->name(),
            'email' => fake()->safeEmail(),
            'comment' => fake()->paragraph(),
            'rating' => fake()->randomFloat(2, 1, 5),
            'status' => fake()->boolean(70), // 70% chance of being approved
        ];
    }

    /**
     * Create an approved rating.
     *
     * @return $this
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 1,
        ]);
    }

    /**
     * Create a pending rating.
     *
     * @return $this
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 0,
        ]);
    }

    /**
     * Create a high rating (4-5 stars).
     *
     * @return $this
     */
    public function highRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => fake()->randomFloat(2, 4, 5),
            'comment' => fake()->randomElement([
                'Excellent product! Highly recommended.',
                'Great quality and fast delivery.',
                'Perfect fit for my car. Very satisfied.',
                'Outstanding service and product quality.',
            ]),
        ]);
    }

    /**
     * Create a low rating (1-2 stars).
     *
     * @return $this
     */
    public function lowRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => fake()->randomFloat(2, 1, 2),
            'comment' => fake()->randomElement([
                'Product quality was not as expected.',
                'Delivery was delayed and product had issues.',
                'Not compatible with my car model.',
                'Poor quality for the price.',
            ]),
        ]);
    }

    /**
     * Create a rating for specific product.
     *
     * @param int $productId
     * @return $this
     */
    public function forProduct(int $productId): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $productId,
        ]);
    }
}
