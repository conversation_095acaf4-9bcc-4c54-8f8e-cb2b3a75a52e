<?php

namespace Database\Factories;

use App\Models\ProductRating;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductRating>
 */
class ProductRatingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // تعليقات قصيرة واقعية لقطع الغيار
        $comments = [
            'منتج ممتاز وجودة عالية',
            'سعر مناسب وتوصيل سريع',
            'قطعة أصلية كما هو مطلوب',
            'جودة جيدة ولكن السعر مرتفع قليلاً',
            'منتج رائع وخدمة ممتازة',
            'التوصيل كان سريع والمنتج كما هو متوقع',
            'جودة ممتازة وسعر معقول',
            'قطعة غيار أصلية بجودة عالية',
            'راضي عن المنتج والخدمة',
            'منتج جيد ولكن التغليف يحتاج تحسين',
            'سرعة في التوصيل وجودة في المنتج',
            'قطعة مطابقة للمواصفات تماماً',
            'خدمة عملاء ممتازة ومنتج أصلي',
            'جودة عالية وسعر تنافسي',
            'منتج موصى به بشدة',
            'تجربة شراء ممتازة',
            'قطعة غيار بجودة أصلية',
            'سعر جيد مقارنة بالسوق',
            'منتج يستحق الشراء',
            'خدمة توصيل سريعة ومنتج ممتاز',
        ];

        return [
            'product_id' => Product::factory(),
            'username' => fake()->name(),
            'email' => fake()->safeEmail(),
            'comment' => fake()->randomElement($comments),
            'rating' => fake()->randomFloat(2, 1, 5),
            'status' => fake()->boolean(70), // 70% chance of being approved
        ];
    }

    /**
     * Create an approved rating.
     *
     * @return $this
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 1,
        ]);
    }

    /**
     * Create a pending rating.
     *
     * @return $this
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 0,
        ]);
    }

    /**
     * Create a high rating (4-5 stars).
     *
     * @return $this
     */
    public function highRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => fake()->randomFloat(2, 4, 5),
            'comment' => fake()->randomElement([
                'منتج ممتاز وجودة عالية جداً',
                'قطعة أصلية وخدمة رائعة',
                'جودة ممتازة وسعر مناسب',
                'منتج موصى به بشدة',
                'تجربة شراء ممتازة',
                'قطعة مطابقة للمواصفات تماماً',
                'خدمة عملاء ممتازة ومنتج أصلي',
                'سرعة في التوصيل وجودة عالية',
            ]),
        ]);
    }

    /**
     * Create a low rating (1-2 stars).
     *
     * @return $this
     */
    public function lowRating(): static
    {
        return $this->state(fn (array $attributes) => [
            'rating' => fake()->randomFloat(2, 1, 2),
            'comment' => fake()->randomElement([
                'جودة أقل من المتوقع',
                'التوصيل متأخر والمنتج به مشاكل',
                'غير متوافق مع موديل سيارتي',
                'جودة ضعيفة مقارنة بالسعر',
                'المنتج لا يطابق الوصف',
                'خدمة عملاء سيئة',
                'التغليف سيء والمنتج تالف',
            ]),
        ]);
    }

    /**
     * Create a rating for specific product.
     *
     * @param int $productId
     * @return $this
     */
    public function forProduct(int $productId): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $productId,
        ]);
    }
}
