<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // تصنيفات واقعية لقطع غيار السيارات
        $autoPartsCategories = [
            [
                'name' => 'فلاتر السيارات',
                'description' => 'فلاتر الهواء والزيت والوقود والمكيف لجميع أنواع السيارات',
                'keywords' => 'فلاتر, فلتر هواء, فلتر زيت, فلتر وقود, فلتر مكيف'
            ],
            [
                'name' => 'نظام الفرامل',
                'description' => 'فحمات وأقراص الفرامل وسوائل الفرامل لضمان الأمان',
                'keywords' => 'فرامل, فحمات فرامل, أقراص فرامل, سائل فرامل'
            ],
            [
                'name' => 'النظام الكهربائي',
                'description' => 'بطاريات وشمعات إشعال وكويلات وأسلاك كهربائية',
                'keywords' => 'بطارية, شمعات إشعال, كويلات, أسلاك كهربائية'
            ],
            [
                'name' => 'الإطارات والعجلات',
                'description' => 'إطارات عالية الجودة وجنوط وإكسسوارات العجلات',
                'keywords' => 'إطارات, جنوط, عجلات, كفرات'
            ],
            [
                'name' => 'نظام التعليق',
                'description' => 'ممتصات الصدمات ونوابض وقطع نظام التعليق',
                'keywords' => 'ممتص صدمات, نوابض, تعليق, مساعدين'
            ],
            [
                'name' => 'المحرك وقطعه',
                'description' => 'قطع غيار المحرك والزيوت والسوائل',
                'keywords' => 'محرك, زيت محرك, جوانات, مكابس'
            ],
            [
                'name' => 'نظام التبريد',
                'description' => 'رديتر ومروحة تبريد وخراطيم وسوائل تبريد',
                'keywords' => 'رديتر, مروحة, تبريد, سائل تبريد'
            ],
            [
                'name' => 'الإضاءة',
                'description' => 'مصابيح أمامية وخلفية وكشافات ضباب',
                'keywords' => 'مصابيح, إضاءة, كشافات, لمبات'
            ],
            [
                'name' => 'الهيكل والبودي',
                'description' => 'قطع الهيكل الخارجي والمرايا والمقابض',
                'keywords' => 'هيكل, بودي, مرايا, مقابض'
            ],
            [
                'name' => 'نظام العادم',
                'description' => 'شكمان وأنابيب العادم وكاتم الصوت',
                'keywords' => 'شكمان, عادم, كاتم صوت'
            ],
            [
                'name' => 'نظام الوقود',
                'description' => 'طرمبة وقود وخزان وقود وأنابيب الوقود',
                'keywords' => 'طرمبة وقود, خزان وقود, أنابيب وقود'
            ],
            [
                'name' => 'التكييف والتدفئة',
                'description' => 'كمبروسر مكيف وفلاتر ومبخر وكوندنسر',
                'keywords' => 'مكيف, كمبروسر, مبخر, كوندنسر'
            ]
        ];

        $category = fake()->randomElement($autoPartsCategories);
        $categoryName = $category['name'];

        // مسارات صور واقعية لقطع الغيار
        $categoryImages = [
            'categories/filters.jpg',
            'categories/brakes.jpg',
            'categories/electrical.jpg',
            'categories/tires.jpg',
            'categories/suspension.jpg',
            'categories/engine.jpg',
            'categories/cooling.jpg',
            'categories/lighting.jpg',
            'categories/body.jpg',
            'categories/exhaust.jpg',
            'categories/fuel.jpg',
            'categories/ac.jpg'
        ];

        return [
            'name' => $categoryName,
            'slug' => Str::slug($categoryName . '-' . fake()->randomNumber(3)),
            'description' => $category['description'],
            'image' => fake()->randomElement($categoryImages),
            'is_showing' => fake()->boolean(80), // 80% احتمال أن يكون ظاهر
            'is_popular' => fake()->boolean(30), // 30% احتمال أن يكون شائع
            'meta_title' => $categoryName . ' - قطع غيار السيارات',
            'meta_description' => $category['description'] . ' - أفضل الأسعار وأعلى جودة في مصر',
            'meta_keywords' => $category['keywords'] . ', قطع غيار, سيارات, مصر',
        ];
    }

    /**
     * إنشاء تصنيف شائع
     */
    public function popular(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_popular' => 1,
            'is_showing' => 1,
        ]);
    }

    /**
     * إنشاء تصنيف مخفي
     */
    public function hidden(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_showing' => 0,
        ]);
    }

    /**
     * إنشاء تصنيف ظاهر
     */
    public function visible(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_showing' => 1,
        ]);
    }
}
