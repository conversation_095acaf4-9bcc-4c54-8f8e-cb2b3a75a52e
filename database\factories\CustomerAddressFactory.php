<?php

namespace Database\Factories;

use App\Models\CustomerAddress;
use App\Models\User;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CustomerAddress>
 */
class CustomerAddressFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'name' => fake()->name(),
            'email' => fake()->safeEmail(),
            'mobile' => fake()->phoneNumber(),
            'country_id' => Country::factory(),
            'address' => fake()->streetAddress(),
            'address2' => fake()->optional()->secondaryAddress(),
            'city' => fake()->city(),
            'state' => fake()->state(),
            'zip' => fake()->postcode(),
        ];
    }

    /**
     * Create address for specific user.
     *
     * @param int $userId
     * @return $this
     */
    public function forUser(int $userId): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $userId,
        ]);
    }

    /**
     * Create Egyptian address.
     *
     * @return $this
     */
    public function egyptian(): static
    {
        return $this->state(fn (array $attributes) => [
            'country_id' => Country::factory()->egypt(),
            'city' => fake()->randomElement(['Cairo', 'Alexandria', 'Giza', 'Shubra El Kheima', 'Port Said']),
            'state' => fake()->randomElement(['Cairo', 'Alexandria', 'Giza', 'Port Said', 'Suez']),
            'zip' => fake()->numerify('#####'),
        ]);
    }

    /**
     * Create address with specific country.
     *
     * @param int $countryId
     * @return $this
     */
    public function inCountry(int $countryId): static
    {
        return $this->state(fn (array $attributes) => [
            'country_id' => $countryId,
        ]);
    }
}
