<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Product;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users and products
        $users = User::where('type', 0)->take(10)->get(); // Get regular users
        $products = Product::take(20)->get();

        if ($users->isEmpty() || $products->isEmpty()) {
            $this->command->warn('No users or products found. Please run UserSeeder and ProductSeeder first.');
            return;
        }

        // Create 20 orders with different statuses
        foreach ($users as $user) {
            // Create 2-3 orders per user
            $orderCount = rand(2, 3);

            for ($i = 0; $i < $orderCount; $i++) {
                // Create order
                $order = Order::factory()->create([
                    'user_id' => $user->id,
                    'country_id' => $user->country_id,
                    'name' => $user->name,
                    'email' => $user->email,
                ]);

                // Add 1-4 items to each order
                $itemCount = rand(1, 4);
                $orderSubtotal = 0;

                for ($j = 0; $j < $itemCount; $j++) {
                    $product = $products->random();
                    $qty = rand(1, 3);
                    $price = $product->selling_price;
                    $total = $qty * $price;
                    $orderSubtotal += $total;

                    OrderItem::factory()->create([
                        'order_id' => $order->id,
                        'product_id' => $product->id,
                        'name' => $product->name,
                        'qty' => $qty,
                        'price' => $price,
                        'total' => $total,
                    ]);
                }

                // Update order totals
                $shipping = 30; // Fixed shipping for simplicity
                $discount = 0;
                $grandTotal = $orderSubtotal + $shipping - $discount;

                $order->update([
                    'subtotal' => $orderSubtotal,
                    'shipping' => $shipping,
                    'discount' => $discount,
                    'grand_total' => $grandTotal,
                ]);
            }
        }

        // Create some specific status orders
        Order::factory(5)->pending()->create();
        Order::factory(8)->paid()->create();
        Order::factory(3)->delivered()->create();
        Order::factory(2)->unpaid()->create();
    }
}
