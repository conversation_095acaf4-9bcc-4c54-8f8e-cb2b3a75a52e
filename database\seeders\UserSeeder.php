<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Country;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Egypt country if it doesn't exist
        $egypt = Country::firstOrCreate(
            ['code' => 'EG'],
            ['name' => 'Egypt']
        );

        // Create admin user
        User::factory()->admin()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'country_id' => $egypt->id,
            'email_verified_at' => now(),
        ]);

        // Create test user
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'country_id' => $egypt->id,
            'email_verified_at' => now(),
        ]);

        // Create 20 random users
        User::factory(20)->create();

        // Create 5 admin users
        User::factory(5)->admin()->create();

        // Create 3 inactive users
        User::factory(3)->inactive()->create();
    }
}
