<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Country;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // الحصول على مصر
        $egypt = Country::where('code', 'EG')->first();

        if (!$egypt) {
            $egypt = Country::create([
                'code' => 'EG',
                'name' => 'Egypt'
            ]);
        }

        // إنشاء مستخدم إداري
        User::factory()->admin()->create([
            'name' => 'مدير الموقع',
            'email' => '<EMAIL>',
            'country_id' => $egypt->id,
            'email_verified_at' => now(),
            'password' => bcrypt('123456789'),
        ]);

        // إنشاء مستخدم عادي
        User::factory()->create([
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'country_id' => $egypt->id,
            'email_verified_at' => now(),
            'password' => bcrypt('123456789'),
            'type' => 0, // مستخدم عادي
        ]);
    }
}
