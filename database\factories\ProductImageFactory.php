<?php

namespace Database\Factories;

use App\Models\ProductImage;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductImage>
 */
class ProductImageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_id' => Product::factory(),
            'image_path' => fake()->imageUrl(640, 480, 'auto parts'),
        ];
    }

    /**
     * Create image for specific product.
     *
     * @param int $productId
     * @return $this
     */
    public function forProduct(int $productId): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $productId,
        ]);
    }

    /**
     * Create auto parts specific images.
     *
     * @return $this
     */
    public function autoParts(): static
    {
        return $this->state(fn (array $attributes) => [
            'image_path' => fake()->randomElement([
                'products/brake-pads.jpg',
                'products/engine-oil.jpg',
                'products/air-filter.jpg',
                'products/spark-plugs.jpg',
                'products/battery.jpg',
                'products/tires.jpg',
                'products/headlights.jpg',
                'products/radiator.jpg',
            ]),
        ]);
    }

    /**
     * Create high resolution image.
     *
     * @return $this
     */
    public function highRes(): static
    {
        return $this->state(fn (array $attributes) => [
            'image_path' => fake()->imageUrl(1200, 800, 'auto parts'),
        ]);
    }

    /**
     * Create thumbnail image.
     *
     * @return $this
     */
    public function thumbnail(): static
    {
        return $this->state(fn (array $attributes) => [
            'image_path' => fake()->imageUrl(300, 200, 'auto parts'),
        ]);
    }
}
