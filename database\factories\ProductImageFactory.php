<?php

namespace Database\Factories;

use App\Models\ProductImage;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductImage>
 */
class ProductImageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // مسارات صور واقعية لقطع الغيار
        $autoPartsImages = [
            'products/air-filter.jpg',
            'products/oil-filter.jpg',
            'products/fuel-filter.jpg',
            'products/cabin-filter.jpg',
            'products/brake-pads-front.jpg',
            'products/brake-pads-rear.jpg',
            'products/brake-discs.jpg',
            'products/engine-oil.jpg',
            'products/transmission-oil.jpg',
            'products/brake-fluid.jpg',
            'products/car-battery.jpg',
            'products/spark-plugs.jpg',
            'products/ignition-coils.jpg',
            'products/headlights.jpg',
            'products/tail-lights.jpg',
            'products/tires.jpg',
            'products/shock-absorbers.jpg',
            'products/radiator.jpg',
            'products/radiator-fan.jpg',
            'products/oxygen-sensor.jpg',
            'products/temperature-sensor.jpg',
            'products/water-pump.jpg',
            'products/fuel-pump.jpg',
            'products/alternator-belt.jpg',
            'products/ac-belt.jpg',
            'products/clutch.jpg',
            'products/flywheel.jpg',
            'products/head-gasket.jpg',
            'products/oil-pan-gasket.jpg',
            'products/side-mirror.jpg',
            'products/door-handle.jpg',
            'products/windshield.jpg',
            'products/rear-glass.jpg',
            'products/fog-lights.jpg',
            'products/turn-signals.jpg',
        ];

        return [
            'product_id' => Product::factory(),
            'image_path' => fake()->randomElement($autoPartsImages),
        ];
    }

    /**
     * Create image for specific product.
     *
     * @param int $productId
     * @return $this
     */
    public function forProduct(int $productId): static
    {
        return $this->state(fn (array $attributes) => [
            'product_id' => $productId,
        ]);
    }

    /**
     * Create auto parts specific images.
     *
     * @return $this
     */
    public function autoParts(): static
    {
        return $this->state(fn (array $attributes) => [
            'image_path' => fake()->randomElement([
                'products/brake-pads-premium.jpg',
                'products/engine-oil-synthetic.jpg',
                'products/air-filter-performance.jpg',
                'products/spark-plugs-platinum.jpg',
                'products/battery-premium.jpg',
                'products/tires-performance.jpg',
                'products/headlights-led.jpg',
                'products/radiator-aluminum.jpg',
            ]),
        ]);
    }

    /**
     * Create main product image.
     *
     * @return $this
     */
    public function mainImage(): static
    {
        return $this->state(fn (array $attributes) => [
            'image_path' => fake()->randomElement([
                'products/main/brake-system-main.jpg',
                'products/main/engine-parts-main.jpg',
                'products/main/electrical-main.jpg',
                'products/main/filters-main.jpg',
            ]),
        ]);
    }

    /**
     * Create detail image.
     *
     * @return $this
     */
    public function detailImage(): static
    {
        return $this->state(fn (array $attributes) => [
            'image_path' => fake()->randomElement([
                'products/details/part-detail-1.jpg',
                'products/details/part-detail-2.jpg',
                'products/details/part-detail-3.jpg',
                'products/details/part-detail-4.jpg',
            ]),
        ]);
    }
}
