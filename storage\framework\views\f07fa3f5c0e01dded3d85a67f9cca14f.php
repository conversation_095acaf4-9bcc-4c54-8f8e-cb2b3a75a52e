<?php $__env->startSection('TitlePage' , $product->name); ?>
<?php $__env->startSection('content'); ?>
    <!-- Breadcrumb Start -->
    <div class="container-fluid">
        <div class="row px-xl-5">
            <div class="col-12">
                <nav class="breadcrumb bg-light mb-30">
                    <a class="breadcrumb-item text-dark" href="#"><?php echo e(__('breadcrumb.home')); ?></a>
                    <a class="breadcrumb-item text-dark" href="#"><?php echo e(__('breadcrumb.shop')); ?></a>
                    <span class="breadcrumb-item active"><?php echo e(__('breadcrumb.shop_detail')); ?></span>
                </nav>
            </div>
        </div>
    </div>
    <!-- Breadcrumb End -->

    <!-- Shop Detail Start -->
    <?php
    use Illuminate\Support\Facades\Auth;
    ?>
    <div class="container-fluid pb-5">
        <div class="row px-xl-5">
            <div class="col-lg-5 mb-30">
                <div id="product-carousel" class="carousel slide" data-ride="carousel">
                    <div class="carousel-inner bg-light">
                        <?php if($product->images->isEmpty()): ?>
                            <div class=" active">
                                <img class="image-product" src="<?php echo e(asset('admin/assets/img/product/noimage.png')); ?>" alt="<?php echo e($product->name); ?>">
                            </div>
                        <?php else: ?>
                            <?php $__currentLoopData = $product->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($image && $image->image_path): ?>
                                    <div class=" <?php echo e($loop->first ? 'active' : ''); ?>">
                                        <img class="image-product" src="<?php echo e(asset($image->image_path)); ?>" alt="<?php echo e($product->name); ?>">
                                    </div>
                                <?php else: ?>
                                    <div class=" <?php echo e($loop->first ? 'active' : ''); ?>">
                                        <img class="image-product" src="<?php echo e(asset('admin/assets/img/product/noimage.png')); ?>" alt="<?php echo e($product->name); ?>">
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                    <a class="carousel-control-prev" href="#product-carousel" data-slide="prev">
                        <i class="fa fa-2x fa-angle-left text-dark"></i>
                    </a>
                    <a class="carousel-control-next" href="#product-carousel" data-slide="next">
                        <i class="fa fa-2x fa-angle-right text-dark"></i>
                    </a>
                </div>
            </div>

            <div class="col-lg-7 h-auto mb-30">
                <div class="h-100 bg-light p-30">
                    <h3><?php echo e($product->name); ?></h3>
                    <?php if($product->selling_price < $product->price): ?>
                    <div class="badge bg-danger text-white position-absolute" style="top: 10px; left: 10px; z-index: 1; padding: 5px 10px; border-radius: 3px;">
                        <?php echo e(round((($product->price - $product->selling_price) / $product->price) * 100)); ?>% <?php echo e(__('product.discount')); ?>

                    </div>
                    <?php endif; ?>
                    <div class="d-flex mb-3">
                        <div class="text-primary star-rating mt-2" title="<?php echo e($product->avgRatingPer); ?>%">
                            <div class="back-stars">
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <div class="front-stars" style="width: <?php echo e($product->avgRatingPer); ?>%">
                                    <small class="fa fa-star"></small>
                                    <small class="fa fa-star"></small>
                                    <small class="fa fa-star"></small>
                                    <small class="fa fa-star"></small>
                                    <small class="fa fa-star"></small>
                                </div>
                            </div>
                        </div>
                        <small class="pt-1">
                            (<?php echo e($product->product_ratings_count); ?>

                            <?php echo e($product->product_ratings_count > 1 ? __('product.reviews') : __('product.review')); ?>)
                        </small>

                    </div>
                    <div class="d-flex mb-3">
                        <a class="btn btn-sm btn-primary me-2 rounded ps-3 pe-3" href="<?php echo e(route('website.category_slug' , $product->category->slug)); ?>"><?php echo e($product->category->name); ?></a>
                        <?php if($product->brand): ?>
                        <a class="btn btn-sm btn-outline-primary rounded ps-3 pe-3" href="<?php echo e(route('website.shop')); ?>?brand=<?php echo e($product->brand_id); ?>"><?php echo e($product->brand->name); ?></a>
                        <?php endif; ?>
                    </div>
                    <h3 class="font-weight-semi-bold mb-4"><?php echo e($product->selling_price); ?> <?php echo e(__('product.egp')); ?>

                        <?php if($product->selling_price < $product->price): ?>
                            <del class="text-muted ml-2"><?php echo e($product->price); ?> <?php echo e(__('product.egp')); ?></del>
                        <?php endif; ?>
                    </h3>
                    <p class="mb-3"><?php echo e($product->short_description); ?></p>
                    <div class="mb-3">
                        <strong class="text-dark mr-1"><?php echo e(__('product.availability')); ?>:</strong>
                        <?php if($product->qty >= $product->minqty): ?>
                            <small class="badge bg-primary p-2 text-muted"><?php echo e(__('product.available')); ?></small>
                        <?php else: ?>
                            <small class="badge bg-danger p-2"><?php echo e(__('product.unavailable')); ?></small>
                        <?php endif; ?>
                    </div>
                    <?php if(!Auth::check()): ?>
                        <p class="fw-bold">
                            <?php echo __('product.login_prompt', ['link' => '<a href="' . route('login') . '">' . __('product.login') . '</a>']); ?>

                        </p>
                    <?php endif; ?>

                    <?php if($product->qty >= $product->minqty): ?>
                    <div class="d-flex align-items-center mb-4 pt-2">
                        <div class="input-group quantity mr-3" style="width: 130px;">
                            <div class="input-group-btn">
                                <button class="btn btn-primary btn-minus">
                                    <i class="fa fa-minus"></i>
                                </button>
                            </div>
                            <input type="text" class="form-control bg-secondary border-0 text-center" id="qty_value" value="1">
                            <div class="input-group-btn">
                                <button class="btn btn-primary btn-plus">
                                    <i class="fa fa-plus"></i>
                                </button>
                            </div>
                        </div>
                        <a class="btn btn-primary px-3" onclick="addtocart()" ><i class="fa fa-shopping-cart mr-1"></i> <?php echo e(__('product.add_to_cart')); ?></a>
                        <input type="hidden" id="product_id" name="product_id" value="<?php echo e($product->id); ?>" />
                    </div>
                    <?php endif; ?>
                    <?php
                        $productUrl = urlencode(request()->fullUrl());
                        $productTitle = urlencode($product->name);
                    ?>

                    <div class="d-flex pt-2">
                        <strong class="text-dark mr-2"><?php echo e(__('product.share_on')); ?> :</strong>
                        <div class="d-inline-flex">
                            <a class="text-dark px-2" href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e($productUrl); ?>" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a class="text-dark px-2" href="https://api.whatsapp.com/send?text=<?php echo e($productTitle); ?>%20<?php echo e($productUrl); ?>" target="_blank">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                            <a class="text-dark px-2" href="https://twitter.com/intent/tweet?url=<?php echo e($productUrl); ?>&text=<?php echo e($productTitle); ?>" target="_blank">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a class="text-dark px-2" href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e($productUrl); ?>" target="_blank">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a class="text-dark px-2" href="https://pinterest.com/pin/create/button/?url=<?php echo e($productUrl); ?>&description=<?php echo e($productTitle); ?>" target="_blank">
                                <i class="fab fa-pinterest"></i>
                            </a>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="row px-xl-5">
            <div class="col">
                <div class="bg-light p-30">
                    <div class="nav nav-tabs mb-4">
                        <a class="nav-item nav-link text-dark active" data-toggle="tab" href="#tab-pane-1"><?php echo e(__('product.description')); ?></a>
                        
                        <a class="nav-item nav-link text-dark" data-toggle="tab" href="#reviews">
                            <?php echo e(__('product.reviews')); ?> (<?php echo e($product->product_ratings_count); ?>

                            <?php echo e($product->product_ratings_count > 1 ? __('product.reviews') : __('product.review')); ?>)
                        </a>
                    </div>
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="tab-pane-1">
                            <h4 class="mb-3"><?php echo e(__('product.description')); ?></h4>
                            <p><?php echo $product->description; ?></p>
                        </div>
                        
                        <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                            <div class="row">
                                <div class="col-md-7">
                                    <form action="<?php echo e(route('rating.save', $product->id)); ?>" name="productRatingForm" id="productRatingForm" method="post">
                                    <?php echo csrf_field(); ?>
                                    <div class="row">
                                            <h3 class="h4 pb-3"><?php echo e(__('product.write_review')); ?></h3>
                                            <div class="form-group col-md-6 mb-3">
                                                <label for="name"><?php echo e(__('product.name')); ?></label>
                                                <input type="text" class="form-control" name="name" id="name" placeholder="<?php echo e(__('product.name')); ?>">
                                                <p></p>
                                            </div>
                                            <div class="form-group col-md-6 mb-3">
                                                <label for="email"><?php echo e(__('product.email')); ?></label>
                                                <input type="text" class="form-control" name="email" id="email" placeholder="<?php echo e(__('product.email')); ?>">
                                                <p></p>
                                            </div>
                                            <div class="form-group mb-3">
                                                <label for="rating"><?php echo e(__('product.rating')); ?></label>
                                                <br>
                                                <div class="rating ms-1" id="rating" style="width: 10rem">
                                                    <input id="rating-5" type="radio" name="rating" value="5"/><label for="rating-5"><i class="fas fa-3x fa-star"></i></label>
                                                    <input id="rating-4" type="radio" name="rating" value="4"  /><label for="rating-4"><i class="fas fa-3x fa-star"></i></label>
                                                    <input id="rating-3" type="radio" name="rating" value="3"/><label for="rating-3"><i class="fas fa-3x fa-star"></i></label>
                                                    <input id="rating-2" type="radio" name="rating" value="2"/><label for="rating-2"><i class="fas fa-3x fa-star"></i></label>
                                                    <input id="rating-1" type="radio" name="rating" value="1"/><label for="rating-1"><i class="fas fa-3x fa-star"></i></label>
                                                </div>
                                                <p></p>
                                            </div>
                                            <div class="form-group mb-3">
                                                <label for=""><?php echo e(__('product.overall_experience')); ?></label>
                                                <textarea name="comment"  id="comment" class="form-control" cols="30" rows="10" placeholder="<?php echo e(__('product.overall_experience')); ?>"></textarea>
                                                <p></p>
                                            </div>
                                            <div>
                                                <?php if(!Auth::user()): ?>
                                                    <p class="fw-bold">
                                                        <?php echo __('product.must_login', ['link' => '<a href="' . route('login') . '">' . __('product.login') . '</a>']); ?>

                                                    </p>
                                                <?php endif; ?>
                                                <button type="submit" <?php echo e(!Auth::user() ? 'disabled' : ''); ?> class="btn btn-primary"><?php echo e(__('product.submit')); ?></button>
                                                </div>
                                            </div>
                                        </form>
                                </div>
                                <div class="col-md-5 mt-5">
                                    <div class="overall-rating mb-3">
                                        <div class="d-flex">
                                            <h1 class="h3 pe-3">
                                                <?php echo e($product->avgRatingPer > 0 ? number_format($product->avgRatingPer / 100, 1) : '0.0'); ?>

                                            </h1>
                                            <div class="star-rating mt-2" title="<?php echo e($product->avgRatingPer); ?>%">
                                                <div class="back-stars">
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <div class="front-stars" style="width: <?php echo e($product->avgRatingPer); ?>%">
                                                        <i class="fa fa-star"></i>
                                                        <i class="fa fa-star"></i>
                                                        <i class="fa fa-star"></i>
                                                        <i class="fa fa-star"></i>
                                                        <i class="fa fa-star"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="pt-2 ps-2">
                                                (<?php echo e($product->product_ratings_count); ?>

                                                <?php echo e($product->product_ratings_count > 1 ? __('product.reviews') : __('product.review')); ?>)
                                            </div>
                                        </div>
                                    </div>

                                    <?php $__currentLoopData = $product->product_ratings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rating): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php $ratingPer = ($rating->rating * 100) / 5; ?>
                                        <div class="rating-group mb-4">
                                            <span><strong><?php echo e($rating->username); ?></strong></span>
                                            <div class="star-rating mt-2" title="<?php echo e($ratingPer); ?>%">
                                                <div class="back-stars">
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <i class="fa fa-star"></i>
                                                    <div class="front-stars" style="width: <?php echo e($ratingPer); ?>%">
                                                        <i class="fa fa-star"></i>
                                                        <i class="fa fa-star"></i>
                                                        <i class="fa fa-star"></i>
                                                        <i class="fa fa-star"></i>
                                                        <i class="fa fa-star"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="my-3">
                                                <p><?php echo e($rating->comment); ?></p>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>

                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Shop Detail End -->

    <?php if($relatedProducts->isNotEmpty()): ?>
    <!-- Other products related to the category Start -->
        <div class="container-fluid py-5">
            <h2 class="section-title position-relative text-uppercase mx-xl-5 mb-4">
                <span class="bg-secondary pr-3"><?php echo e(__('product.related_to_category')); ?></span>
            </h2>
            <div class="row px-xl-5">
                <?php $__currentLoopData = $relatedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-3 col-md-4 col-sm-6 pb-1">
                        <div class="product-item bg-light mb-4">
                            <div class="product-img position-relative overflow-hidden">
                                <?php
                                $image = $product->images->first();
                                ?>
                                <?php if($image && $image->image_path): ?>
                                    <img class="img-fluid image-Custom" src="<?php echo e(asset($image->image_path)); ?>" alt="<?php echo e($product->name); ?>" style="height: 250px; width: 100%;">
                                <?php else: ?>
                                    <img src="<?php echo e(asset('admin/assets/img/product/noimage.png')); ?>" alt="<?php echo e($product->name); ?>" style="height: 250px; width: 100%;" class="img-fluid w-100">
                                <?php endif; ?>
                                <?php if($product->selling_price < $product->price): ?>
                                <div class="badge bg-danger text-white position-absolute" style="top: 10px; left: 10px; z-index: 1; padding: 5px 10px; border-radius: 3px;">
                                    <?php echo e(round((($product->price - $product->selling_price) / $product->price) * 100)); ?>% <?php echo e(__('product.discount')); ?>

                                </div>
                                <?php endif; ?>
                                <div class="product-action">
                                    <?php if($product->qty >= $product->minqty): ?>
                                    <a class="btn btn-outline-dark btn-square add-to-cart" data-toggle="tooltip" title="<?php echo e(__('product.add_to_cart')); ?>"
                                    data-product-id="<?php echo e($product->id); ?>"
                                    href="javascript:void(0);">
                                    <i class="fa fa-cart-plus"></i></a>
                                    <?php else: ?>
                                    <a class="btn btn-outline-dark btn-square" data-toggle="tooltip" title="<?php echo e(__('product.unavailable')); ?>"><i class="fa-solid fa-store-slash"></i></a>
                                    <?php endif; ?>
                                    <a class="btn btn-outline-dark btn-square" onclick="addToWishlist(<?php echo e($product->id); ?>)" href="javascript:void(0);" data-toggle="tooltip" title="<?php echo e(__('product.add_wishlist')); ?>"><i class="far fa-heart"></i></a>
                                    <a class="btn btn-outline-dark btn-square" href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>"data-toggle="tooltip" title="<?php echo e(__('product.view_deatils')); ?>"><i class="fa-solid fa-eye"></i></a>
                                </div>
                            </div>
                            <div class="text-center py-4">
                                <a class="h6 text-decoration-none" href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>" style="display: block; height: 40px; overflow: hidden;"><?php echo e($product->name); ?></a>
                                <div class="d-flex align-items-center justify-content-center mt-1">
                                    <span class="text-muted small">
                                        <a href="<?php echo e(route('website.category_slug', $product->category->slug)); ?>" class="text-muted"><?php echo e($product->category->name); ?></a>
                                        <?php if($product->brand): ?>
                                            | <a href="<?php echo e(route('website.shop')); ?>?brand=<?php echo e($product->brand_id); ?>" class="text-muted"><?php echo e($product->brand->name); ?></a>
                                        <?php endif; ?>
                                    </span>
                                </div>
                                <div class="d-flex align-items-center justify-content-center mt-2">
                                    <h5><?php echo e($product->selling_price); ?> <?php echo e(__('product.egp')); ?></h5>
                                    <?php if($product->selling_price < $product->price): ?>
                                    <h6 class="text-muted ml-2"><del><?php echo e($product->price); ?> <?php echo e(__('product.egp')); ?></del></h6>
                                    <?php endif; ?>
                                </div>
                                <div class="d-flex align-items-center justify-content-center mb-1">
                                    <div class="back-stars">
                                        <small class="fa fa-star"></small>
                                        <small class="fa fa-star"></small>
                                        <small class="fa fa-star"></small>
                                        <small class="fa fa-star"></small>
                                        <small class="fa fa-star"></small>
                                        <div class="front-stars" style="width: <?php echo e($product->avgRatingPer); ?>%">
                                            <small class="fa fa-star"></small>
                                            <small class="fa fa-star"></small>
                                            <small class="fa fa-star"></small>
                                            <small class="fa fa-star"></small>
                                            <small class="fa fa-star"></small>
                                        </div>
                                    </div>
                                    <small class="pt-1"> (<?php echo e($product->product_ratings_count); ?>)</small>
                                </div>
                                <div class="d-flex align-items-center justify-content-center mt-2">
                                    <a href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>" class="btn btn-primary"><?php echo e(__('main.show_details')); ?> <i class="fa-solid fa-arrow-right"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    <!-- Other products related to the category End -->
    <?php endif; ?>


    <!-- You May Also Like Start -->
    <div class="container-fluid py-5">
        <h2 class="section-title position-relative text-uppercase mx-xl-5 mb-4">
            <span class="bg-secondary pr-3"><?php echo e(__('product.may_also_like')); ?></span>
        </h2>
        <div class="row px-xl-5">
            <?php $__currentLoopData = $trendingProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-3 col-md-4 col-sm-6 pb-1">
                <div class="product-item bg-light mb-4">
                    <div class="product-img position-relative overflow-hidden">
                        <?php
                        $image = $product->images->first();
                        ?>
                        <?php if($image && $image->image_path): ?>
                            <img class="img-fluid image-Custom" src="<?php echo e(asset($image->image_path)); ?>" alt="<?php echo e($product->name); ?>" style="height: 250px; width: 100%;">
                        <?php else: ?>
                            <img src="<?php echo e(asset('admin/assets/img/product/noimage.png')); ?>" alt="<?php echo e($product->name); ?>" style="height: 250px; width: 100%;" class="img-fluid w-100">
                        <?php endif; ?>
                        <?php if($product->selling_price < $product->price): ?>
                                <div class="badge bg-danger text-white position-absolute" style="top: 10px; left: 10px; z-index: 1; padding: 5px 10px; border-radius: 3px;">
                                    <?php echo e(round((($product->price - $product->selling_price) / $product->price) * 100)); ?>% <?php echo e(__('product.discount')); ?>

                                </div>
                        <?php endif; ?>
                         <div class="product-action">
                                    <?php if($product->qty >= $product->minqty): ?>
                                    <a class="btn btn-outline-dark btn-square add-to-cart" data-toggle="tooltip" title="<?php echo e(__('product.add_to_cart')); ?>"
                                    data-product-id="<?php echo e($product->id); ?>"
                                    href="javascript:void(0);">
                                    <i class="fa fa-cart-plus"></i></a>
                                    <?php else: ?>
                                    <a class="btn btn-outline-dark btn-square" data-toggle="tooltip" title="<?php echo e(__('product.unavailable')); ?>"><i class="fa-solid fa-store-slash"></i></a>
                                    <?php endif; ?>
                                    <a class="btn btn-outline-dark btn-square" onclick="addToWishlist(<?php echo e($product->id); ?>)" href="javascript:void(0);" data-toggle="tooltip" title="<?php echo e(__('product.add_wishlist')); ?>"><i class="far fa-heart"></i></a>
                                    <a class="btn btn-outline-dark btn-square" href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>"data-toggle="tooltip" title="<?php echo e(__('product.view_deatils')); ?>"><i class="fa-solid fa-eye"></i></a>
                                </div>
                    </div>
                    <div class="text-center py-4">
                        <a class="h6 text-decoration-none" href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>" style="display: block; height: 40px; overflow: hidden;"><?php echo e($product->name); ?></a>
                        <div class="d-flex align-items-center justify-content-center mt-1">
                            <span class="text-muted small">
                                <a href="<?php echo e(route('website.category_slug', $product->category->slug)); ?>" class="text-muted"><?php echo e($product->category->name); ?></a>
                                <?php if($product->brand): ?>
                                 | <a href="<?php echo e(route('website.shop')); ?>?brand=<?php echo e($product->brand_id); ?>" class="text-muted"><?php echo e($product->brand->name); ?></a>
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="d-flex align-items-center justify-content-center mt-2">
                            <h5><?php echo e($product->selling_price); ?> <?php echo e(__('product.egp')); ?></h5>
                            <?php if($product->selling_price < $product->price): ?>
                            <h6 class="text-muted ml-2"><del><?php echo e($product->price); ?> <?php echo e(__('product.egp')); ?></del></h6>
                            <?php endif; ?>
                        </div>
                        <div class="d-flex align-items-center justify-content-center mb-1">
                            <div class="back-stars">
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <small class="fa fa-star"></small>
                                <div class="front-stars" style="width: <?php echo e($product->avgRatingPer); ?>%">
                                    <small class="fa fa-star"></small>
                                    <small class="fa fa-star"></small>
                                    <small class="fa fa-star"></small>
                                    <small class="fa fa-star"></small>
                                    <small class="fa fa-star"></small>
                                </div>
                            </div>
                            <small class="pt-1"> (<?php echo e($product->product_ratings_count); ?>)</small>
                        </div>
                        <div class="d-flex align-items-center justify-content-center mt-2">
                            <a href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>" class="btn btn-primary"><?php echo e(__('main.show_details')); ?> <i class="fa-solid fa-arrow-right"></i></a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <!-- You May Also Like End -->

    <?php $__env->startSection('customjs'); ?>

    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function addtocart() {
            var product_id = $('#product_id').val();
            var qty = $('#qty_value').val();

            console.log('Product ID: ' + product_id + ' | Quantity: ' + qty);

            $.ajax({
                method: 'POST',
                url: "<?php echo e(route('product.addToCart')); ?>",
                data: {
                    _token: "<?php echo e(csrf_token()); ?>",
                    product_id: product_id,
                    quantity: qty
                },
                success: function(response) {
                    Swal.fire({
                        icon: response.icon,
                        text: response.msg,
                        timer: 2000,
                        timerProgressBar: true,
                    });
                    updateCartCount();
                },
                error: function(xhr, status, error) {
                    console.error('Error: ' + error);
                    console.error(xhr.responseText);
                }
            });
        }


        $(document).ready(function () {
            $("#productRatingForm").submit(function (event) {
                event.preventDefault();

                $.ajax({
                    type: "POST",
                    url: $("#productRatingForm").attr("action"),
                    data: $(this).serialize(),
                    dataType: "json",
                    success: function (response) {
                        if (response.status) {
                            Swal.fire({
                                title: response.title,
                                text: response.message,
                                icon: "success",
                                confirmButtonText: "OK",
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: response.title || "Error",
                                text: response.message || "Something went wrong. Please try again later.",
                                icon: "warning",
                                confirmButtonText: "OK",
                            });
                        }
                    },
                    error: function (xhr) {
                        if (xhr.status === 401) {
                            Swal.fire("Unauthorized!", "You need to log in to submit a review.", "warning");
                        } else if (xhr.status === 422) {
                            let response = xhr.responseJSON;
                            Swal.fire({
                                title: response.title || "Error",
                                text: response.message || "You have already submitted a review for this product.",
                                icon: "warning",
                                confirmButtonText: "OK",
                            });
                        } else {
                            Swal.fire("Error!", "Something went wrong. Please try again later.", "error");
                        }
                    },
                });
            });
        });





        function handleErrors(errors) {
            var fields = ['name', 'email', 'comment', 'rating'];

            fields.forEach(function(field) {
                if (errors[field]) {
                    $("#" + field).addClass('is-invalid')
                        .siblings('p')
                        .addClass('invalid-feedback')
                        .html(errors[field]);
                } else {
                    $("#" + field).removeClass('is-invalid')
                        .siblings('p')
                        .removeClass('invalid-feedback')
                        .html('');
                }
            });
        }
    </style>
    <?php $__env->stopSection(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('website.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/website/product.blade.php ENDPATH**/ ?>