<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء 10 تصنيفات واقعية لقطع غيار السيارات

        // تصنيفات شائعة (6 تصنيفات)
        \App\Models\Category::factory()->popular()->create([
            'name' => 'فلاتر السيارات',
            'slug' => 'car-filters',
            'image' => 'categories/filters.jpg'
        ]);

        \App\Models\Category::factory()->popular()->create([
            'name' => 'نظام الفرامل',
            'slug' => 'brake-system',
            'image' => 'categories/brakes.jpg'
        ]);

        \App\Models\Category::factory()->popular()->create([
            'name' => 'النظام الكهربائي',
            'slug' => 'electrical-system',
            'image' => 'categories/electrical.jpg'
        ]);

        \App\Models\Category::factory()->popular()->create([
            'name' => 'الإطارات والعجلات',
            'slug' => 'tires-wheels',
            'image' => 'categories/tires.jpg'
        ]);

        \App\Models\Category::factory()->popular()->create([
            'name' => 'المحرك وقطعه',
            'slug' => 'engine-parts',
            'image' => 'categories/engine.jpg'
        ]);

        \App\Models\Category::factory()->popular()->create([
            'name' => 'الإضاءة',
            'slug' => 'lighting',
            'image' => 'categories/lighting.jpg'
        ]);

        // تصنيفات عادية (4 تصنيفات)
        \App\Models\Category::factory()->visible()->create([
            'name' => 'نظام التعليق',
            'slug' => 'suspension-system',
            'image' => 'categories/suspension.jpg'
        ]);

        \App\Models\Category::factory()->visible()->create([
            'name' => 'نظام التبريد',
            'slug' => 'cooling-system',
            'image' => 'categories/cooling.jpg'
        ]);

        \App\Models\Category::factory()->visible()->create([
            'name' => 'نظام الوقود',
            'slug' => 'fuel-system',
            'image' => 'categories/fuel.jpg'
        ]);

        \App\Models\Category::factory()->visible()->create([
            'name' => 'التكييف والتدفئة',
            'slug' => 'ac-heating',
            'image' => 'categories/ac.jpg'
        ]);
    }
}
