<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class SiteSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create main site settings
        SiteSetting::factory()->arabic()->create([
            'site_name' => [
                'en' => 'Cairo Auto Parts',
                'ar' => 'قطع غيار القاهرة'
            ],
            'email' => '<EMAIL>',
            'phone_number' => '+20 ************',
            'hotline' => '+20 ************',
            'company_description' => [
                'en' => 'Leading supplier of high-quality auto parts in Egypt. We provide genuine and aftermarket parts for all car brands.',
                'ar' => 'المورد الرائد لقطع غيار السيارات عالية الجودة في مصر. نوفر قطع غيار أصلية وبديلة لجميع ماركات السيارات.'
            ],
            'address' => [
                'en' => '123 Auto Parts Street, Cairo, Egypt',
                'ar' => '123 شارع قطع الغيار، القاهرة، مصر'
            ],
            'working_hours' => 'Sunday - Thursday: 9:00 AM - 6:00 PM | Friday: 2:00 PM - 6:00 PM',
            'facebook_link' => 'https://facebook.com/cairoautoparts',
            'whatsapp_number' => '+20 ************',
            'twitter_link' => 'https://twitter.com/cairoautoparts',
            'linkedin_link' => 'https://linkedin.com/company/cairoautoparts',
        ]);
    }
}
