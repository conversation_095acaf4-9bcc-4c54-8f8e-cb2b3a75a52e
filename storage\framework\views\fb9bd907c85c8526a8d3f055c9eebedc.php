<div class="container-fluid">
    <div class="row bg-secondary py-1 px-xl-5">
        <div class="col-lg-6 d-none d-lg-block">
            <div class="d-inline-flex align-items-center h-100">
                <a class="text-body mr-3" href="<?php echo e(route('website.about')); ?>"><?php echo e(__('navbar.about')); ?></a>
                <a class="text-body mr-3" href="<?php echo e(url('page/contact-us')); ?>"><?php echo e(__('navbar.contact')); ?></a>
            </div>
        </div>
        <div class="col-lg-6 text-right text-lg-right">
            <div class="d-inline-flex align-items-center">
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-light dropdown-toggle" data-toggle="dropdown">
                        <?php echo e(auth()->check() ? __('navbar.welcome_user', ['name' => auth()->user()->name]) : __('navbar.Sign_in_or_create_an_account')); ?>

                    </button>


                    <div class="dropdown-menu dropdown-menu-right">
                        <?php if(Route::has('login')): ?>
                        <?php if(auth()->guard()->check()): ?>
                        <a class="dropdown-item btn" href="<?php echo e(route('website.account.profile')); ?>"><?php echo e(__('navbar.my_account')); ?></a>
                        <a class="dropdown-item btn" href="<?php echo e(route('website.account.orders')); ?>"><?php echo e(__('navbar.my_orders')); ?></a>
                        <a class="dropdown-item btn" href="<?php echo e(route('website.cart')); ?>"><?php echo e(__('navbar.cart')); ?></a>
                        <a class="dropdown-item btn" href="<?php echo e(route('website.account.wishlist')); ?>"><?php echo e(__('navbar.my_wishlist')); ?></a>
                        <a class="dropdown-item btn" href="<?php echo e(route('logout')); ?>"><?php echo e(__('navbar.logout')); ?></a>
                        <?php else: ?>
                        <a class="dropdown-item btn" href="<?php echo e(route('login')); ?>"><?php echo e(__('navbar.login')); ?></a>
                        <?php if(Route::has('register')): ?>
                        <a class="dropdown-item btn" href="<?php echo e(route('register')); ?>"><?php echo e(__('navbar.register')); ?></a>
                        <?php endif; ?>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="dropdown ms-2">
                    <button class="btn btn-sm btn-light dropdown-toggle d-flex align-items-center" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="fi fi-<?php echo e(app()->getLocale() == 'en' ? 'us' : (app()->getLocale() == 'ar' ? 'eg' : app()->getLocale())); ?> me-2"></span>
                        <?php echo e(strtoupper(app()->getLocale())); ?>

                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                        <?php $__currentLoopData = LaravelLocalization::getSupportedLocales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $localeCode => $properties): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li>
                                <a class="dropdown-item d-flex align-items-center <?php echo e(app()->getLocale() == $localeCode ? 'active' : ''); ?>"
                                    href="<?php echo e(LaravelLocalization::getLocalizedURL($localeCode, null, [], true)); ?>">
                                    <span class="fi fi-<?php echo e($localeCode == 'en' ? 'us' : ($localeCode == 'ar' ? 'eg' : $localeCode)); ?> me-2"></span>
                                    <?php echo e($properties['native']); ?>

                                </a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
            <div class="d-inline-flex align-items-center d-block d-lg-none">
                <a href="" class="btn px-0 ml-2">
                    <i class="fas fa-heart text-dark"></i>
                    <span class="badge text-dark border border-dark rounded-circle" style="padding-bottom: 2px;">0</span>
                </a>
                <a href="" class="btn px-0 ml-2">
                    <i class="fas fa-shopping-cart text-dark"></i>
                    <span class="badge text-dark border border-dark rounded-circle" style="padding-bottom: 2px;">0</span>
                </a>
            </div>
        </div>
    </div>
    <div class="row align-items-center bg-light py-3 px-xl-5 d-none d-lg-flex">
        <div class="col-lg-4">
            <a href="#" class="text-decoration-none">
                <?php
                    $siteName = $site_settings->site_name ;
                    $nameParts = explode(' ', $siteName);
                ?>

                <?php if(count($nameParts) == 1): ?>
                    <span class="h1 text-uppercase text-primary bg-dark px-2"><?php echo e($nameParts[0]); ?></span>
                <?php elseif(count($nameParts) == 2): ?>
                    <span class="h1 text-uppercase text-primary bg-dark px-2"><?php echo e($nameParts[0]); ?></span>
                    <span class="h1 text-uppercase text-dark bg-primary px-2 ml-n1"><?php echo e($nameParts[1]); ?></span>
                <?php else: ?>
                    <span class="h1 text-uppercase text-primary bg-dark px-2"><?php echo e($nameParts[0]); ?></span>
                    <span class="h1 text-uppercase text-dark bg-primary px-2 ml-n1"><?php echo e($nameParts[1]); ?></span>
                    <span class="h1 text-uppercase text-primary bg-dark px-2"><?php echo e(implode(' ', array_slice($nameParts, 2))); ?></span>
                <?php endif; ?>
            </a>
        </div>

        <div class="col-lg-4">
            <form action="<?php echo e(route('website.shop')); ?>" method="get">
                <div class="input-group">
                    <input value="<?php echo e(Request::get('search')); ?>" type="text" class="form-control" name="search" id="search" placeholder="<?php echo e(__('navbar.search_placeholder')); ?>" aria-label="Search" aria-describedby="button-addon1">
                    <div class="input-group-append">
                        <button type="submit" class="input-group-text bg-transparent text-primary rounded-0">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <div class="col-lg-4 text-lg-right ml-auto">
            <div>
                <p class="m-0"><?php echo e(__('navbar.call_us')); ?></p>
                <h5 class="m-0"><?php echo e($site_settings->hotline); ?></h5>
            </div>
        </div>
    </div>


</div>

<?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/website/layouts/inc/topbar.blade.php ENDPATH**/ ?>