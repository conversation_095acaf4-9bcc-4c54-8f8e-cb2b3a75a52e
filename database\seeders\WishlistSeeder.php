<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Wishlist;
use App\Models\User;
use App\Models\Product;

class WishlistSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users and products
        $users = User::where('type', 0)->take(20)->get(); // Get regular users
        $products = Product::take(50)->get();

        if ($users->isEmpty() || $products->isEmpty()) {
            $this->command->warn('No users or products found. Please run UserSeeder and ProductSeeder first.');
            return;
        }

        // Create wishlist items for users
        foreach ($users as $user) {
            // Each user has 2-8 items in wishlist
            $wishlistItemCount = rand(2, 8);
            $selectedProducts = $products->random($wishlistItemCount);

            foreach ($selectedProducts as $product) {
                // Check if this combination already exists to avoid duplicates
                $exists = Wishlist::where('user_id', $user->id)
                    ->where('product_id', $product->id)
                    ->exists();

                if (!$exists) {
                    Wishlist::factory()->create([
                        'user_id' => $user->id,
                        'product_id' => $product->id,
                    ]);
                }
            }
        }

        // Create some additional random wishlist items
        Wishlist::factory(30)->create();
    }
}
