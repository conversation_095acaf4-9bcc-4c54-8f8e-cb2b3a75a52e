<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StoreCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
        'name'          => 'required|string|max:255',
        'slug'          => 'required',
        'description'   => 'required|string',
        'is_showing'    => 'nullable',
        'is_popular'    => 'nullable',
        'image'         => 'required|image|mimes:jpg,jpeg,png,gif',
        'meta_title'    => 'required|string|max:255',
        'meta_description' => 'required|string|max:500',
        'meta_keywords' => 'required|string|max:255',
        ];
    }
}
