<?php

namespace Database\Factories;

use App\Models\ShippingCharges;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ShippingCharges>
 */
class ShippingChargesFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'country_id' => Country::factory(),
            'amount' => fake()->randomFloat(2, 10, 100),
        ];
    }

    /**
     * Create shipping charges for Egypt.
     *
     * @return $this
     */
    public function egypt(): static
    {
        return $this->state(fn (array $attributes) => [
            'country_id' => Country::factory()->egypt(),
            'amount' => fake()->randomFloat(2, 20, 50), // EGP
        ]);
    }

    /**
     * Create shipping charges for USA.
     *
     * @return $this
     */
    public function usa(): static
    {
        return $this->state(fn (array $attributes) => [
            'country_id' => Country::factory()->usa(),
            'amount' => fake()->randomFloat(2, 15, 75), // USD
        ]);
    }

    /**
     * Create free shipping.
     *
     * @return $this
     */
    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => 0.00,
        ]);
    }

    /**
     * Create expensive shipping.
     *
     * @return $this
     */
    public function expensive(): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => fake()->randomFloat(2, 100, 200),
        ]);
    }

    /**
     * Create shipping charges for specific country.
     *
     * @param int $countryId
     * @return $this
     */
    public function forCountry(int $countryId): static
    {
        return $this->state(fn (array $attributes) => [
            'country_id' => $countryId,
        ]);
    }
}
