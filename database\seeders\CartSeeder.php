<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Cart;
use App\Models\User;
use App\Models\Product;

class CartSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users and products
        $users = User::where('type', 0)->take(15)->get(); // Get regular users
        $products = Product::take(30)->get();

        if ($users->isEmpty() || $products->isEmpty()) {
            $this->command->warn('No users or products found. Please run UserSeeder and ProductSeeder first.');
            return;
        }

        // Create cart items for users
        foreach ($users as $user) {
            // Each user has 1-5 items in cart
            $cartItemCount = rand(1, 5);
            $selectedProducts = $products->random($cartItemCount);

            foreach ($selectedProducts as $product) {
                Cart::factory()->create([
                    'user_id' => $user->id,
                    'product_id' => $product->id,
                    'name' => $product->name,
                    'qty' => rand(1, 3),
                    'selling_price' => $product->selling_price,
                ]);
            }
        }

        // Create some additional random cart items
        Cart::factory(20)->create();
    }
}
