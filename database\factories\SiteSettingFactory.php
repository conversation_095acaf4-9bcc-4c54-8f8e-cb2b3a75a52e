<?php

namespace Database\Factories;

use App\Models\SiteSetting;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SiteSetting>
 */
class SiteSettingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'site_name' => [
                'en' => fake()->company() . ' Auto Parts',
                'ar' => 'قطع غيار ' . fake()->company(),
            ],
            'site_image' => fake()->imageUrl(200, 100, 'business'),
            'map_link' => fake()->url(),
            'phone_number' => fake()->phoneNumber(),
            'company_description' => [
                'en' => fake()->paragraph(3),
                'ar' => 'وصف الشركة باللغة العربية',
            ],
            'hotline' => fake()->phoneNumber(),
            'address' => [
                'en' => fake()->address(),
                'ar' => 'العنوان باللغة العربية',
            ],
            'email' => fake()->companyEmail(),
            'facebook_link' => 'https://facebook.com/' . fake()->userName(),
            'whatsapp_number' => fake()->phoneNumber(),
            'twitter_link' => 'https://twitter.com/' . fake()->userName(),
            'linkedin_link' => 'https://linkedin.com/company/' . fake()->userName(),
            'working_hours' => 'Sunday - Thursday: 9:00 AM - 6:00 PM',
        ];
    }

    /**
     * Create Arabic-focused site settings.
     *
     * @return $this
     */
    public function arabic(): static
    {
        return $this->state(fn (array $attributes) => [
            'site_name' => [
                'en' => 'Cairo Auto Parts',
                'ar' => 'قطع غيار القاهرة',
            ],
            'company_description' => [
                'en' => 'Leading auto parts supplier in Egypt',
                'ar' => 'المورد الرائد لقطع غيار السيارات في مصر',
            ],
            'address' => [
                'en' => 'Cairo, Egypt',
                'ar' => 'القاهرة، مصر',
            ],
            'working_hours' => 'الأحد - الخميس: 9:00 ص - 6:00 م',
        ]);
    }
}
