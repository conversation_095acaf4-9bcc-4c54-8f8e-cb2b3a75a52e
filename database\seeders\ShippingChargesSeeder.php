<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ShippingCharges;
use App\Models\Country;

class ShippingChargesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get or create Egypt
        $egypt = Country::firstOrCreate(
            ['code' => 'EG'],
            ['name' => 'Egypt']
        );

        // Create shipping charges for Egypt
        ShippingCharges::factory()->create([
            'country_id' => $egypt->id,
            'amount' => 30.00, // 30 EGP
        ]);

        // Create shipping charges for other countries
        $countries = [
            ['name' => 'United States', 'code' => 'US', 'amount' => 25.00],
            ['name' => 'United Kingdom', 'code' => 'GB', 'amount' => 20.00],
            ['name' => 'Germany', 'code' => 'DE', 'amount' => 18.00],
            ['name' => 'France', 'code' => 'FR', 'amount' => 18.00],
            ['name' => 'Saudi Arabia', 'code' => 'SA', 'amount' => 35.00],
            ['name' => 'United Arab Emirates', 'code' => 'AE', 'amount' => 40.00],
            ['name' => 'Kuwait', 'code' => 'KW', 'amount' => 35.00],
            ['name' => 'Qatar', 'code' => 'QA', 'amount' => 35.00],
        ];

        foreach ($countries as $countryData) {
            $country = Country::firstOrCreate(
                ['code' => $countryData['code']],
                ['name' => $countryData['name']]
            );

            ShippingCharges::factory()->create([
                'country_id' => $country->id,
                'amount' => $countryData['amount'],
            ]);
        }

        // Create some random shipping charges for other countries
        ShippingCharges::factory(10)->create();
    }
}
