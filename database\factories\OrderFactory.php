<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\User;
use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $subtotal = fake()->randomFloat(2, 100, 2000);
        $discount = fake()->randomFloat(2, 0, $subtotal * 0.2); // up to 20% discount
        $shipping = fake()->randomFloat(2, 10, 50);
        $grandTotal = $subtotal - $discount + $shipping;

        return [
            'user_id' => User::factory(),
            'subtotal' => $subtotal,
            'shipping' => $shipping,
            'coupon_code' => fake()->optional()->regexify('[A-Z]{4}[0-9]{2}'),
            'discount' => $discount,
            'grand_total' => $grandTotal,
            'payment_status' => fake()->randomElement(['paid', 'not paid']),
            'status' => fake()->randomElement(['pending', 'shipped', 'delivered']),

            // Customer address information
            'name' => fake()->name(),
            'email' => fake()->safeEmail(),
            'mobile' => fake()->phoneNumber(),
            'country_id' => Country::factory(),
            'address' => fake()->streetAddress(),
            'address2' => fake()->optional()->secondaryAddress(),
            'city' => fake()->city(),
            'state' => fake()->state(),
            'zip' => fake()->postcode(),
            'notes' => fake()->optional()->sentence(),
            'shipped_date' => null, // Will be set when order is shipped
        ];
    }

    /**
     * Create a pending order.
     *
     * @return $this
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'not paid',
            'status' => 'pending',
        ]);
    }

    /**
     * Create a paid order.
     *
     * @return $this
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'paid',
            'status' => 'shipped',
            'shipped_date' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Create a delivered order.
     *
     * @return $this
     */
    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'paid',
            'status' => 'delivered',
            'shipped_date' => fake()->dateTimeBetween('-2 weeks', '-3 days'),
        ]);
    }

    /**
     * Create an unpaid order.
     *
     * @return $this
     */
    public function unpaid(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'not paid',
            'status' => 'pending',
        ]);
    }
}
