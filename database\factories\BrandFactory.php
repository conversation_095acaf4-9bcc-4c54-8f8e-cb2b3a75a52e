<?php

namespace Database\Factories;

use App\Models\Brand;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Brand>
 */
class BrandFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        // علامات تجارية واقعية لقطع غيار السيارات
        $autoPartsBrands = [
            'Bosch',
            'Denso',
            'NGK',
            'Mahle',
            'Febi',
            'Mann Filter',
            'Brembo',
            'Sachs',
            'Valeo',
            'Continental',
            'Hella',
            'TRW'
        ];

        $brandName = fake()->randomElement($autoPartsBrands);

        // مسارات صور العلامات التجارية
        $brandImages = [
            'brands/bosch.jpg',
            'brands/denso.jpg',
            'brands/ngk.jpg',
            'brands/mahle.jpg',
            'brands/febi.jpg',
            'brands/mann-filter.jpg',
            'brands/brembo.jpg',
            'brands/sachs.jpg',
            'brands/valeo.jpg',
            'brands/continental.jpg',
            'brands/hella.jpg',
            'brands/trw.jpg'
        ];

        return [
            'name' => $brandName,
            'slug' => Str::slug($brandName . '-' . fake()->randomNumber(3)),
            'status' => fake()->boolean(90), // 90% احتمال أن تكون نشطة
            'image' => fake()->randomElement($brandImages),
        ];
    }

    /**
     * إنشاء علامة تجارية نشطة
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 1,
        ]);
    }

    /**
     * إنشاء علامة تجارية غير نشطة
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 0,
        ]);
    }
}
