<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء منتجات عادية
        \App\Models\Product::factory(30)->create();

        // إنشاء منتجات رائجة
        \App\Models\Product::factory(10)->trending()->create();

        // إنشاء منتجات مخفضة السعر
        \App\Models\Product::factory(8)->onSale()->create();

        // إنشاء منتجات فاخرة
        \App\Models\Product::factory(5)->premium()->create();

        // إنشاء منتجات اقتصادية
        \App\Models\Product::factory(12)->budget()->create();

        // إنشاء منتجات بكمية قليلة
        \App\Models\Product::factory(6)->lowStock()->create();

        // إنشاء منتجات نفدت كميتها
        \App\Models\Product::factory(3)->outOfStock()->create();

        // إنشاء منتجات غير نشطة
        \App\Models\Product::factory(4)->inactive()->create();

        // إضافة صور للمنتجات
        $products = \App\Models\Product::all();
        foreach ($products as $product) {
            // إضافة 1-4 صور لكل منتج
            $imageCount = rand(1, 4);
            \App\Models\ProductImage::factory($imageCount)->create([
                'product_id' => $product->id
            ]);
        }
    }
}
