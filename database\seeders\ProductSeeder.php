<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء 20 منتج فقط بتنوع مناسب

        // منتجات عادية (10 منتجات)
        \App\Models\Product::factory(10)->create();

        // منتجات رائجة (4 منتجات)
        \App\Models\Product::factory(4)->trending()->create();

        // منتجات مخفضة السعر (3 منتجات)
        \App\Models\Product::factory(3)->onSale()->create();

        // منتجات اقتصادية (2 منتج)
        \App\Models\Product::factory(2)->budget()->create();

        // منتج فاخر واحد
        \App\Models\Product::factory(1)->premium()->create();

        // إضافة صور للمنتجات
        $products = \App\Models\Product::all();
        foreach ($products as $product) {
            // إضافة 2-3 صور لكل منتج
            $imageCount = rand(2, 3);
            \App\Models\ProductImage::factory($imageCount)->create([
                'product_id' => $product->id
            ]);
        }
    }
}
