<?php

namespace Database\Factories;

use App\Models\OrderItem;
use App\Models\Order;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderItem>
 */
class OrderItemFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $qty = fake()->numberBetween(1, 5);
        $price = fake()->randomFloat(2, 10, 500);
        $total = $qty * $price;

        return [
            'order_id' => Order::factory(),
            'product_id' => Product::factory(),
            'name' => fake()->words(3, true),
            'qty' => $qty,
            'price' => $price,
            'total' => $total,
        ];
    }

    /**
     * Create order item with specific quantity.
     *
     * @param int $quantity
     * @return $this
     */
    public function withQuantity(int $quantity): static
    {
        return $this->state(function (array $attributes) use ($quantity) {
            $price = $attributes['price'] ?? fake()->randomFloat(2, 10, 500);
            return [
                'qty' => $quantity,
                'total' => $quantity * $price,
            ];
        });
    }

    /**
     * Create order item with specific price.
     *
     * @param float $price
     * @return $this
     */
    public function withPrice(float $price): static
    {
        return $this->state(function (array $attributes) use ($price) {
            $qty = $attributes['qty'] ?? fake()->numberBetween(1, 5);
            return [
                'price' => $price,
                'total' => $qty * $price,
            ];
        });
    }
}
