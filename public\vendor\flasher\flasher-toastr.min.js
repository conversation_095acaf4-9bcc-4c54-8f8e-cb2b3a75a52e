!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@flasher/flasher"),require("jquery")):"function"==typeof define&&define.amd?define(["@flasher/flasher","jquery"],e):((t="undefined"!=typeof globalThis?globalThis:t||self).flasher=t.flasher||{},t.flasher.toastr=e(t.flasher,t.jQuery))}(this,(function(t,e){"use strict";var o=function(){return o=Object.assign||function(t){for(var e,o=1,i=arguments.length;o<i;o++)for(var n in e=arguments[o])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},o.apply(this,arguments)},i={};!function(t){var o;o=function(t){return function(){var e,o,i,n=0,a="error",s="info",r="success",c="warning",d={clear:function(o,i){var n=u();e||p(n),l(o,n,i)||function(o){for(var i=e.children(),n=i.length-1;n>=0;n--)l(t(i[n]),o)}(n)},remove:function(o){var i=u();e||p(i),o&&0===t(":focus",o).length?f(o):e.children().length&&e.remove()},error:function(t,e,o){return g({type:a,iconClass:u().iconClasses.error,message:t,optionsOverride:o,title:e})},getContainer:p,info:function(t,e,o){return g({type:s,iconClass:u().iconClasses.info,message:t,optionsOverride:o,title:e})},options:{},subscribe:function(t){o=t},success:function(t,e,o){return g({type:r,iconClass:u().iconClasses.success,message:t,optionsOverride:o,title:e})},version:"2.1.4",warning:function(t,e,o){return g({type:c,iconClass:u().iconClasses.warning,message:t,optionsOverride:o,title:e})}};return d;function p(o,i){return o||(o=u()),(e=t("#"+o.containerId)).length||i&&(e=function(o){return(e=t("<div/>").attr("id",o.containerId).addClass(o.positionClass)).appendTo(t(o.target)),e}(o)),e}function l(e,o,i){var n=!(!i||!i.force)&&i.force;return!(!e||!n&&0!==t(":focus",e).length||(e[o.hideMethod]({duration:o.hideDuration,easing:o.hideEasing,complete:function(){f(e)}}),0))}function A(t){o&&o(t)}function g(o){var a=u(),s=o.iconClass||a.iconClass;if(void 0!==o.optionsOverride&&(a=t.extend(a,o.optionsOverride),s=o.optionsOverride.iconClass||s),!function(t,e){if(t.preventDuplicates){if(e.message===i)return!0;i=e.message}return!1}(a,o)){n++,e=p(a,!0);var r=null,c=t("<div/>"),d=t("<div/>"),l=t("<div/>"),g=t("<div/>"),h=t(a.closeHtml),m={intervalId:null,hideEta:null,maxHideTime:null},b={toastId:n,state:"visible",startTime:new Date,options:a,map:o};return o.iconClass&&c.addClass(a.toastClass).addClass(s),function(){if(o.title){var t=o.title;a.escapeHtml&&(t=x(o.title)),d.append(t).addClass(a.titleClass),c.append(d)}}(),function(){if(o.message){var t=o.message;a.escapeHtml&&(t=x(o.message)),l.append(t).addClass(a.messageClass),c.append(l)}}(),a.closeButton&&(h.addClass(a.closeClass).attr("role","button"),c.prepend(h)),a.progressBar&&(g.addClass(a.progressClass),c.prepend(g)),a.rtl&&c.addClass("rtl"),a.newestOnTop?e.prepend(c):e.append(c),function(){var t="";switch(o.iconClass){case"toast-success":case"toast-info":t="polite";break;default:t="assertive"}c.attr("aria-live",t)}(),c.hide(),c[a.showMethod]({duration:a.showDuration,easing:a.showEasing,complete:a.onShown}),a.timeOut>0&&(r=setTimeout(w,a.timeOut),m.maxHideTime=parseFloat(a.timeOut),m.hideEta=(new Date).getTime()+m.maxHideTime,a.progressBar&&(m.intervalId=setInterval(y,10))),a.closeOnHover&&c.hover(C,v),!a.onclick&&a.tapToDismiss&&c.click(w),a.closeButton&&h&&h.click((function(t){t.stopPropagation?t.stopPropagation():void 0!==t.cancelBubble&&!0!==t.cancelBubble&&(t.cancelBubble=!0),a.onCloseClick&&a.onCloseClick(t),w(!0)})),a.onclick&&c.click((function(t){a.onclick(t),w()})),A(b),a.debug&&console&&console.log(b),c}function x(t){return null==t&&(t=""),t.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function w(e){var o=e&&!1!==a.closeMethod?a.closeMethod:a.hideMethod,i=e&&!1!==a.closeDuration?a.closeDuration:a.hideDuration,n=e&&!1!==a.closeEasing?a.closeEasing:a.hideEasing;if(!t(":focus",c).length||e)return clearTimeout(m.intervalId),c[o]({duration:i,easing:n,complete:function(){f(c),clearTimeout(r),a.onHidden&&"hidden"!==b.state&&a.onHidden(),b.state="hidden",b.endTime=new Date,A(b)}})}function v(){(a.timeOut>0||a.extendedTimeOut>0)&&(r=setTimeout(w,a.extendedTimeOut),m.maxHideTime=parseFloat(a.extendedTimeOut),m.hideEta=(new Date).getTime()+m.maxHideTime)}function C(){clearTimeout(r),m.hideEta=0,c.stop(!0,!0)[a.showMethod]({duration:a.showDuration,easing:a.showEasing})}function y(){var t=(m.hideEta-(new Date).getTime())/m.maxHideTime*100;g.width(t+"%")}}function u(){return t.extend({},{tapToDismiss:!0,toastClass:"toast",containerId:"toast-container",debug:!1,showMethod:"fadeIn",showDuration:300,showEasing:"swing",onShown:void 0,hideMethod:"fadeOut",hideDuration:1e3,hideEasing:"swing",onHidden:void 0,closeMethod:!1,closeDuration:!1,closeEasing:!1,closeOnHover:!0,extendedTimeOut:1e3,iconClasses:{error:"toast-error",info:"toast-info",success:"toast-success",warning:"toast-warning"},iconClass:"toast-info",positionClass:"toast-top-right",timeOut:5e3,titleClass:"toast-title",messageClass:"toast-message",escapeHtml:!1,target:"body",closeHtml:'<button type="button">&times;</button>',closeClass:"toast-close-button",newestOnTop:!0,preventDuplicates:!1,progressBar:!1,progressClass:"toast-progress",rtl:!1},d.options)}function f(t){e||(e=p()),t.is(":visible")||(t.remove(),t=null,0===e.children().length&&(e.remove(),i=void 0))}}()},t.exports?t.exports=o(e):window.toastr=o(window.jQuery)}({get exports(){return i},set exports(t){i=t}});var n=i,a=[],s=[];!function(t,e){if(t&&"undefined"!=typeof document){var o,i=!0===e.prepend?"prepend":"append",n=!0===e.singleTag,r="string"==typeof e.container?document.querySelector(e.container):document.getElementsByTagName("head")[0];if(n){var c=a.indexOf(r);-1===c&&(c=a.push(r)-1,s[c]={}),o=s[c]&&s[c][i]?s[c][i]:s[c][i]=d()}else o=d();65279===t.charCodeAt(0)&&(t=t.substring(1)),o.styleSheet?o.styleSheet.cssText+=t:o.appendChild(document.createTextNode(t))}function d(){var t=document.createElement("style");if(t.setAttribute("type","text/css"),e.attributes)for(var o=Object.keys(e.attributes),n=0;n<o.length;n++)t.setAttribute(o[n],e.attributes[o[n]]);var a="prepend"===i?"afterbegin":"beforeend";return r.insertAdjacentElement(a,t),t}}(".toast-title{font-weight:700}.toast-message{-ms-word-wrap:break-word;word-wrap:break-word}.toast-message a,.toast-message label{color:#fff}.toast-message a:hover{color:#ccc;text-decoration:none}.toast-close-button{color:#fff;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=80);filter:alpha(opacity=80);float:right;font-size:20px;font-weight:700;line-height:1;opacity:.8;position:relative;right:-.3em;-webkit-text-shadow:0 1px 0 #fff;text-shadow:0 1px 0 #fff;top:-.3em}.toast-close-button:focus,.toast-close-button:hover{color:#000;cursor:pointer;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=40);filter:alpha(opacity=40);opacity:.4;text-decoration:none}.rtl .toast-close-button{float:left;left:-.3em;right:.3em}button.toast-close-button{-webkit-appearance:none;background:0 0;border:0;cursor:pointer;padding:0}.toast-top-center{right:0;top:0;width:100%}.toast-bottom-center{bottom:0;right:0;width:100%}.toast-top-full-width{right:0;top:0;width:100%}.toast-bottom-full-width{bottom:0;right:0;width:100%}.toast-top-left{left:12px;top:12px}.toast-top-right{right:12px;top:12px}.toast-bottom-right{bottom:12px;right:12px}.toast-bottom-left{bottom:12px;left:12px}#toast-container{pointer-events:none;position:fixed;z-index:999999}#toast-container *{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}#toast-container>div{background-position:15px;background-repeat:no-repeat;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;-moz-box-shadow:0 0 12px #999;-webkit-box-shadow:0 0 12px #999;box-shadow:0 0 12px #999;color:#fff;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=80);filter:alpha(opacity=80);margin:0 0 6px;opacity:.8;overflow:hidden;padding:15px 15px 15px 50px;pointer-events:auto;position:relative;width:300px}#toast-container>div.rtl{background-position:right 15px center;direction:rtl;padding:15px 50px 15px 15px}#toast-container>div:hover{-moz-box-shadow:0 0 12px #000;-webkit-box-shadow:0 0 12px #000;box-shadow:0 0 12px #000;cursor:pointer;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=100);filter:alpha(opacity=100);opacity:1}#toast-container>.toast-info{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=)!important}#toast-container>.toast-error{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=)!important}#toast-container>.toast-success{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADsSURBVEhLY2AYBfQMgf///3P8+/evAIgvA/FsIF+BavYDDWMBGroaSMMBiE8VC7AZDrIFaMFnii3AZTjUgsUUWUDA8OdAH6iQbQEhw4HyGsPEcKBXBIC4ARhex4G4BsjmweU1soIFaGg/WtoFZRIZdEvIMhxkCCjXIVsATV6gFGACs4Rsw0EGgIIH3QJYJgHSARQZDrWAB+jawzgs+Q2UO49D7jnRSRGoEFRILcdmEMWGI0cm0JJ2QpYA1RDvcmzJEWhABhD/pqrL0S0CWuABKgnRki9lLseS7g2AlqwHWQSKH4oKLrILpRGhEQCw2LiRUIa4lwAAAABJRU5ErkJggg==)!important}#toast-container>.toast-warning{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGYSURBVEhL5ZSvTsNQFMbXZGICMYGYmJhAQIJAICYQPAACiSDB8AiICQQJT4CqQEwgJvYASAQCiZiYmJhAIBATCARJy+9rTsldd8sKu1M0+dLb057v6/lbq/2rK0mS/TRNj9cWNAKPYIJII7gIxCcQ51cvqID+GIEX8ASG4B1bK5gIZFeQfoJdEXOfgX4QAQg7kH2A65yQ87lyxb27sggkAzAuFhbbg1K2kgCkB1bVwyIR9m2L7PRPIhDUIXgGtyKw575yz3lTNs6X4JXnjV+LKM/m3MydnTbtOKIjtz6VhCBq4vSm3ncdrD2lk0VgUXSVKjVDJXJzijW1RQdsU7F77He8u68koNZTz8Oz5yGa6J3H3lZ0xYgXBK2QymlWWA+RWnYhskLBv2vmE+hBMCtbA7KX5drWyRT/2JsqZ2IvfB9Y4bWDNMFbJRFmC9E74SoS0CqulwjkC0+5bpcV1CZ8NMej4pjy0U+doDQsGyo1hzVJttIjhQ7GnBtRFN1UarUlH8F3xict+HY07rEzoUGPlWcjRFRr4/gChZgc3ZL2d8oAAAAASUVORK5CYII=)!important}#toast-container.toast-bottom-center>div,#toast-container.toast-top-center>div{margin-left:auto;margin-right:auto;width:300px}#toast-container.toast-bottom-full-width>div,#toast-container.toast-top-full-width>div{margin-left:auto;margin-right:auto;width:96%}.toast{background-color:#030303}.toast-success{background-color:#51a351}.toast-error{background-color:#bd362f}.toast-info{background-color:#2f96b4}.toast-warning{background-color:#f89406}.toast-progress{background-color:#000;bottom:0;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=40);filter:alpha(opacity=40);height:4px;left:0;opacity:.4;position:absolute}@media (max-width:240px){#toast-container>div{padding:8px 8px 8px 50px;width:11em}#toast-container>div.rtl{padding:8px 50px 8px 8px}#toast-container .toast-close-button{right:-.2em;top:-.2em}#toast-container .rtl .toast-close-button{left:-.2em;right:.2em}}@media (min-width:241px) and (max-width:480px){#toast-container>div{padding:8px 8px 8px 50px;width:18em}#toast-container>div.rtl{padding:8px 50px 8px 8px}#toast-container .toast-close-button{right:-.2em;top:-.2em}#toast-container .rtl .toast-close-button{left:-.2em;right:.2em}}@media (min-width:481px) and (max-width:768px){#toast-container>div{padding:15px 15px 15px 50px;width:25em}#toast-container>div.rtl{padding:15px 50px 15px 15px}}",{});var r=new(function(){function t(){}return t.prototype.success=function(t,e,o){this.flash("success",t,e,o)},t.prototype.info=function(t,e,o){this.flash("info",t,e,o)},t.prototype.warning=function(t,e,o){this.flash("warning",t,e,o)},t.prototype.error=function(t,e,o){this.flash("error",t,e,o)},t.prototype.flash=function(t,e,o,i){var n=this.createNotification(t,e,o,i);this.renderOptions({}),this.render({notification:n})},t.prototype.createNotification=function(t,e,o,i){if("object"==typeof t?(t=(i=t).type,e=i.message,o=i.title):"object"==typeof e?(e=(i=e).message,o=i.title):"object"==typeof o&&(o=(i=o).title),void 0===e)throw new Error("message option is required");return{type:t||"info",message:e,title:o,options:i}},t.prototype.render=function(t){var e=t.notification,o=e.message,i=e.title,a=e.options,s=e.type||"info";n[s](o,i,a).parent().attr("data-turbo-cache","false")},t.prototype.renderOptions=function(t){n.options=o({timeOut:t.timeOut||5e3,progressBar:t.progressBar||5e3},t)},t}());return t.addFactory("toastr",r),r}));
