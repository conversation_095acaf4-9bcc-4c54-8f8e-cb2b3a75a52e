<?php

namespace Database\Factories;

use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Country>
 */
class CountryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->country(),
            'code' => fake()->unique()->countryCode(),
        ];
    }

    /**
     * Create Egypt country.
     *
     * @return $this
     */
    public function egypt(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Egypt',
            'code' => 'EG',
        ]);
    }

    /**
     * Create USA country.
     *
     * @return $this
     */
    public function usa(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'United States',
            'code' => 'US',
        ]);
    }
}
