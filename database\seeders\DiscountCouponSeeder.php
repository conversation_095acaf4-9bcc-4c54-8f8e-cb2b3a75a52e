<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DiscountCoupon;
use App\Models\User;

class DiscountCouponSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get admin user
        $admin = User::where('type', 1)->first();
        
        if (!$admin) {
            $admin = User::factory()->admin()->create();
        }

        // Create active percentage discount
        DiscountCoupon::factory()->active()->percentage()->create([
            'code' => 'WELCOME10',
            'name' => 'Welcome Discount',
            'description' => '10% discount for new customers',
            'discount_amount' => 10,
            'min_order_amount' => 100,
            'created_by' => $admin->id,
        ]);

        // Create active fixed discount
        DiscountCoupon::factory()->active()->fixed()->create([
            'code' => 'SAVE50',
            'name' => 'Save 50 EGP',
            'description' => 'Fixed 50 EGP discount on orders above 500 EGP',
            'discount_amount' => 50,
            'min_order_amount' => 500,
            'created_by' => $admin->id,
        ]);

        // Create limited use coupon
        DiscountCoupon::factory()->active()->limitedUse()->create([
            'code' => 'LIMITED20',
            'name' => 'Limited Time Offer',
            'description' => '20% discount - limited uses',
            'discount_type' => 'percentage',
            'discount_amount' => 20,
            'max_uses' => 100,
            'max_uses_user' => 1,
            'created_by' => $admin->id,
        ]);

        // Create expired coupon
        DiscountCoupon::factory()->expired()->create([
            'code' => 'EXPIRED15',
            'name' => 'Expired Discount',
            'description' => 'This coupon has expired',
            'created_by' => $admin->id,
        ]);

        // Create 10 random coupons
        DiscountCoupon::factory(10)->create([
            'created_by' => $admin->id,
        ]);
    }
}
