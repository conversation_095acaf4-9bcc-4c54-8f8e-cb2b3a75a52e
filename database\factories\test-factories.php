<?php

/**
 * Test script to verify all factories work correctly
 * Run this with: php artisan tinker
 * Then copy and paste the code below
 */

// Test basic factories
echo "Testing basic factories...\n";

// Test Country Factory
$country = \App\Models\Country::factory()->egypt()->make();
echo "Country: {$country->name} ({$country->code})\n";

// Test User Factory
$user = \App\Models\User::factory()->admin()->make();
echo "User: {$user->name} - Type: {$user->type}\n";

// Test SiteSetting Factory
$settings = \App\Models\SiteSetting::factory()->arabic()->make();
echo "Site: {$settings->site_name['en']} / {$settings->site_name['ar']}\n";

// Test Category Factory
$category = \App\Models\Category::factory()->make();
echo "Category: {$category->name}\n";

// Test Brand Factory
$brand = \App\Models\Brand::factory()->make();
echo "Brand: {$brand->name}\n";

// Test Product Factory
$product = \App\Models\Product::factory()->make();
echo "Product: {$product->name} - Price: {$product->selling_price}\n";

// Test ProductImage Factory
$image = \App\Models\ProductImage::factory()->make();
echo "Product Image: {$image->image_path}\n";

// Test Cart Factory
$cart = \App\Models\Cart::factory()->make();
echo "Cart Item: {$cart->name} - Qty: {$cart->qty}\n";

// Test Wishlist Factory
$wishlist = \App\Models\Wishlist::factory()->make();
echo "Wishlist: User {$wishlist->user_id} - Product {$wishlist->product_id}\n";

// Test Order Factory
$order = \App\Models\Order::factory()->pending()->make();
echo "Order: {$order->grand_total} - Status: {$order->status}\n";

// Test OrderItem Factory
$orderItem = \App\Models\OrderItem::factory()->make();
echo "Order Item: {$orderItem->name} - Total: {$orderItem->total}\n";

// Test DiscountCoupon Factory
$coupon = \App\Models\DiscountCoupon::factory()->active()->percentage()->make();
echo "Coupon: {$coupon->code} - {$coupon->discount_amount}% off\n";

// Test ShippingCharges Factory
$shipping = \App\Models\ShippingCharges::factory()->egypt()->make();
echo "Shipping: {$shipping->amount}\n";

// Test ProductRating Factory
$rating = \App\Models\ProductRating::factory()->highRating()->make();
echo "Rating: {$rating->rating}/5 - {$rating->username}\n";

// Test CustomerAddress Factory
$address = \App\Models\CustomerAddress::factory()->egyptian()->make();
echo "Address: {$address->city}, {$address->state}\n";

echo "\nAll factories tested successfully!\n";

/**
 * To test with actual database creation, run:
 * 
 * php artisan migrate:fresh
 * php artisan db:seed
 * 
 * This will create all the test data using the factories.
 */
