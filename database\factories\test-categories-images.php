<?php

/**
 * اختبار سريع لـ CategoryFactory و ProductImageFactory المحدثين
 * 
 * لتشغيل هذا الاختبار:
 * php artisan tinker
 * ثم انسخ والصق الكود أدناه
 */

echo "🏷️ اختبار CategoryFactory و ProductImageFactory المحدثين...\n\n";

// 1. اختبار إنشاء تصنيفات واقعية
echo "📂 اختبار التصنيفات الواقعية...\n";
try {
    // إنشاء تصنيف عادي
    $category = \App\Models\Category::factory()->create();
    echo "✅ تم إنشاء تصنيف عادي: {$category->name}\n";
    echo "   - الوصف: {$category->description}\n";
    echo "   - الصورة: {$category->image}\n";
    echo "   - ظاهر: " . ($category->is_showing ? 'نعم' : 'لا') . "\n";
    echo "   - شائع: " . ($category->is_popular ? 'نعم' : 'لا') . "\n";
    
    // إنشاء تصنيف شائع
    $popularCategory = \App\Models\Category::factory()->popular()->create();
    echo "✅ تم إنشاء تصنيف شائع: {$popularCategory->name}\n";
    echo "   - ظاهر: " . ($popularCategory->is_showing ? 'نعم' : 'لا') . "\n";
    echo "   - شائع: " . ($popularCategory->is_popular ? 'نعم' : 'لا') . "\n";
    
    // إنشاء تصنيف مخفي
    $hiddenCategory = \App\Models\Category::factory()->hidden()->create();
    echo "✅ تم إنشاء تصنيف مخفي: {$hiddenCategory->name}\n";
    echo "   - ظاهر: " . ($hiddenCategory->is_showing ? 'نعم' : 'لا') . "\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء التصنيفات: " . $e->getMessage() . "\n";
}
echo "\n";

// 2. اختبار أسماء التصنيفات المتخصصة
echo "🔧 اختبار أسماء التصنيفات المتخصصة...\n";
try {
    $categories = \App\Models\Category::factory(10)->create();
    echo "✅ تم إنشاء 10 تصنيفات:\n";
    
    foreach ($categories as $cat) {
        echo "   - {$cat->name}\n";
    }
    
    // التحقق من أن جميع الأسماء مرتبطة بقطع الغيار
    $autoPartsKeywords = ['فلاتر', 'فرامل', 'كهربائي', 'إطارات', 'تعليق', 'محرك', 'تبريد', 'إضاءة', 'هيكل', 'عادم', 'وقود', 'تكييف'];
    $relevantCategories = 0;
    
    foreach ($categories as $cat) {
        foreach ($autoPartsKeywords as $keyword) {
            if (strpos($cat->name, $keyword) !== false) {
                $relevantCategories++;
                break;
            }
        }
    }
    
    echo "✅ التصنيفات المرتبطة بقطع الغيار: {$relevantCategories}/10\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار أسماء التصنيفات: " . $e->getMessage() . "\n";
}
echo "\n";

// 3. اختبار صور المنتجات
echo "📸 اختبار صور المنتجات...\n";
try {
    // إنشاء منتج للاختبار
    $product = \App\Models\Product::factory()->create();
    echo "✅ تم إنشاء منتج للاختبار: {$product->name['ar']}\n";
    
    // إنشاء صور عادية
    $regularImages = \App\Models\ProductImage::factory(3)->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء {$regularImages->count()} صور عادية:\n";
    foreach ($regularImages as $img) {
        echo "   - {$img->image_path}\n";
    }
    
    // إنشاء صور متخصصة
    $autoPartsImage = \App\Models\ProductImage::factory()->autoParts()->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء صورة متخصصة: {$autoPartsImage->image_path}\n";
    
    // إنشاء صورة رئيسية
    $mainImage = \App\Models\ProductImage::factory()->mainImage()->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء صورة رئيسية: {$mainImage->image_path}\n";
    
    // إنشاء صورة تفاصيل
    $detailImage = \App\Models\ProductImage::factory()->detailImage()->create([
        'product_id' => $product->id
    ]);
    echo "✅ تم إنشاء صورة تفاصيل: {$detailImage->image_path}\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء صور المنتجات: " . $e->getMessage() . "\n";
}
echo "\n";

// 4. اختبار مسارات الصور
echo "🖼️ اختبار مسارات الصور...\n";
try {
    $images = \App\Models\ProductImage::factory(20)->create();
    echo "✅ تم إنشاء 20 صورة منتج\n";
    
    // التحقق من أن جميع المسارات واقعية
    $validPaths = 0;
    $autoPartsImageKeywords = ['filter', 'brake', 'engine', 'battery', 'tire', 'light', 'radiator', 'sensor', 'pump', 'belt', 'clutch', 'gasket', 'mirror', 'handle', 'glass'];
    
    foreach ($images as $img) {
        $path = strtolower($img->image_path);
        foreach ($autoPartsImageKeywords as $keyword) {
            if (strpos($path, $keyword) !== false) {
                $validPaths++;
                break;
            }
        }
    }
    
    echo "✅ الصور المرتبطة بقطع الغيار: {$validPaths}/20\n";
    
    // عرض بعض أمثلة المسارات
    echo "📋 أمثلة مسارات الصور:\n";
    foreach ($images->take(5) as $img) {
        echo "   - {$img->image_path}\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار مسارات الصور: " . $e->getMessage() . "\n";
}
echo "\n";

// 5. اختبار العلاقات
echo "🔗 اختبار العلاقات بين التصنيفات والمنتجات...\n";
try {
    // إنشاء تصنيف مع منتجات
    $categoryWithProducts = \App\Models\Category::factory()->popular()->create();
    $products = \App\Models\Product::factory(5)->create([
        'category_id' => $categoryWithProducts->id
    ]);
    
    echo "✅ تم إنشاء تصنيف '{$categoryWithProducts->name}' مع {$products->count()} منتجات\n";
    
    // إضافة صور للمنتجات
    foreach ($products as $product) {
        $imageCount = rand(2, 4);
        \App\Models\ProductImage::factory($imageCount)->create([
            'product_id' => $product->id
        ]);
    }
    
    $totalImages = \App\Models\ProductImage::where('product_id', 'IN', $products->pluck('id'))->count();
    echo "✅ تم إضافة {$totalImages} صورة للمنتجات\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار العلاقات: " . $e->getMessage() . "\n";
}
echo "\n";

// 6. ملخص الاختبار
echo "📊 ملخص الاختبار:\n";
echo "✅ CategoryFactory يولد تصنيفات واقعية لقطع غيار السيارات\n";
echo "✅ ProductImageFactory يولد مسارات صور واقعية\n";
echo "✅ الحالات (States) تعمل بشكل صحيح\n";
echo "✅ العلاقات بين الجداول تعمل بشكل سليم\n";
echo "✅ جميع البيانات مناسبة لمتجر قطع غيار السيارات\n\n";

echo "🎉 CategoryFactory و ProductImageFactory جاهزان للاستخدام!\n";

/**
 * للتشغيل:
 * 
 * 1. تأكد من تشغيل المايجريشن:
 *    php artisan migrate:fresh
 * 
 * 2. افتح Laravel Tinker:
 *    php artisan tinker
 * 
 * 3. انسخ والصق الكود أعلاه
 * 
 * أو شغل السيدرز مباشرة:
 *    php artisan db:seed --class=CategorySeeder
 */
