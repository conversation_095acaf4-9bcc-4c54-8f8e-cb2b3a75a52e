<?php

/**
 * اختبار نهائي للإعداد المبسط
 *
 * لتشغيل هذا الاختبار:
 * php artisan tinker
 * ثم انسخ والصق الكود أدناه
 */

echo "🧪 اختبار الإعداد النهائي...\n\n";

// 1. اختبار الدول
echo "🌍 اختبار الدول...\n";
$countries = \App\Models\Country::all();
echo "✅ عدد الدول: " . $countries->count() . "\n";

$egypt = \App\Models\Country::where('code', 'EG')->first();
if ($egypt) {
    echo "✅ مصر موجودة: {$egypt->name} ({$egypt->code})\n";
} else {
    echo "❌ مصر غير موجودة!\n";
}

// 2. اختبار المستخدمين
echo "\n👥 اختبار المستخدمين...\n";
$users = \App\Models\User::all();
echo "✅ عدد المستخدمين: " . $users->count() . "\n";

foreach ($users as $user) {
    $userType = $user->type == 1 ? 'إداري' : 'مستخدم عادي';
    $country = $user->country ? $user->country->name : 'غير محدد';
    echo "   - {$user->name} ({$user->email}) - {$userType} - الدولة: {$country}\n";
}

// 3. اختبار تسجيل الدخول
echo "\n🔑 اختبار بيانات تسجيل الدخول...\n";
$admin = \App\Models\User::where('email', '<EMAIL>')->first();
$user = \App\Models\User::where('email', '<EMAIL>')->first();

if ($admin) {
    echo "✅ المدير موجود: {$admin->name} - النوع: " . ($admin->type == 1 ? 'إداري' : 'عادي') . "\n";
} else {
    echo "❌ المدير غير موجود!\n";
}

if ($user) {
    echo "✅ المستخدم موجود: {$user->name} - النوع: " . ($user->type == 0 ? 'عادي' : 'إداري') . "\n";
} else {
    echo "❌ المستخدم غير موجود!\n";
}

// 4. اختبار إعدادات الموقع
echo "\n⚙️ اختبار إعدادات الموقع...\n";
$siteSettings = \App\Models\SiteSetting::all();
echo "✅ عدد إعدادات الموقع: " . $siteSettings->count() . "\n";
if ($siteSettings->count() > 0) {
    $setting = $siteSettings->first();
    echo "   - اسم الموقع: " . $setting->site_name['ar'] . " / " . $setting->site_name['en'] . "\n";
    echo "   - الإيميل: " . $setting->email . "\n";
}

// 5. اختبار التصنيفات
echo "\n📂 اختبار التصنيفات...\n";
$categories = \App\Models\Category::all();
echo "✅ عدد التصنيفات: " . $categories->count() . "\n";
foreach ($categories->take(3) as $category) {
    echo "   - " . $category->name['ar'] . " / " . $category->name['en'] . "\n";
}

// 6. اختبار العلامات التجارية
echo "\n🏷️ اختبار العلامات التجارية...\n";
$brands = \App\Models\Brand::all();
echo "✅ عدد العلامات التجارية: " . $brands->count() . "\n";
foreach ($brands->take(3) as $brand) {
    echo "   - " . $brand->name . "\n";
}

// 7. اختبار المنتجات
echo "\n📦 اختبار المنتجات...\n";
$products = \App\Models\Product::all();
echo "✅ عدد المنتجات: " . $products->count() . "\n";
foreach ($products->take(3) as $product) {
    echo "   - " . $product->name['ar'] . " - " . $product->price . " جنيه\n";
}

// 8. اختبار صور المنتجات
echo "\n📸 اختبار صور المنتجات...\n";
$productImages = \App\Models\ProductImage::all();
echo "✅ عدد صور المنتجات: " . $productImages->count() . "\n";

// 9. ملخص نهائي
echo "\n📋 ملخص الإعداد النهائي:\n";
echo "✅ الدول: " . \App\Models\Country::count() . "\n";
echo "✅ إعدادات الموقع: " . \App\Models\SiteSetting::count() . "\n";
echo "✅ التصنيفات: " . \App\Models\Category::count() . "\n";
echo "✅ العلامات التجارية: " . \App\Models\Brand::count() . "\n";
echo "✅ المنتجات: " . \App\Models\Product::count() . "\n";
echo "✅ صور المنتجات: " . \App\Models\ProductImage::count() . "\n";
echo "✅ المستخدمين: " . \App\Models\User::count() . "\n";

echo "\n🔑 بيانات تسجيل الدخول:\n";
echo "   الإداري: <EMAIL> / 123456789\n";
echo "   المستخدم: <EMAIL> / 123456789\n";

echo "\n🎉 الإعداد المبسط جاهز تماماً!\n";

/**
 * للتشغيل:
 *
 * 1. تشغيل المايجريشن والسيدرز:
 *    php artisan migrate:fresh --seed
 *
 * 2. اختبار الإعداد:
 *    php artisan tinker
 *    ثم انسخ والصق الكود أعلاه
 */
