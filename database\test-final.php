<?php

/**
 * اختبار نهائي للإعداد المبسط
 * 
 * لتشغيل هذا الاختبار:
 * php artisan tinker
 * ثم انسخ والصق الكود أدناه
 */

echo "🧪 اختبار الإعداد النهائي...\n\n";

// 1. اختبار الدول
echo "🌍 اختبار الدول...\n";
$countries = \App\Models\Country::all();
echo "✅ عدد الدول: " . $countries->count() . "\n";

$egypt = \App\Models\Country::where('code', 'EG')->first();
if ($egypt) {
    echo "✅ مصر موجودة: {$egypt->name} ({$egypt->code})\n";
} else {
    echo "❌ مصر غير موجودة!\n";
}

// 2. اختبار المستخدمين
echo "\n👥 اختبار المستخدمين...\n";
$users = \App\Models\User::all();
echo "✅ عدد المستخدمين: " . $users->count() . "\n";

foreach ($users as $user) {
    $userType = $user->type == 1 ? 'إداري' : 'مستخدم عادي';
    $country = $user->country ? $user->country->name : 'غير محدد';
    echo "   - {$user->name} ({$user->email}) - {$userType} - الدولة: {$country}\n";
}

// 3. اختبار تسجيل الدخول
echo "\n🔑 اختبار بيانات تسجيل الدخول...\n";
$admin = \App\Models\User::where('email', '<EMAIL>')->first();
$user = \App\Models\User::where('email', '<EMAIL>')->first();

if ($admin) {
    echo "✅ المدير موجود: {$admin->name} - النوع: " . ($admin->type == 1 ? 'إداري' : 'عادي') . "\n";
} else {
    echo "❌ المدير غير موجود!\n";
}

if ($user) {
    echo "✅ المستخدم موجود: {$user->name} - النوع: " . ($user->type == 0 ? 'عادي' : 'إداري') . "\n";
} else {
    echo "❌ المستخدم غير موجود!\n";
}

// 4. اختبار الجداول الأخرى (يجب أن تكون فارغة)
echo "\n📊 اختبار الجداول الأخرى...\n";
echo "   - التصنيفات: " . \App\Models\Category::count() . " (يجب أن يكون 0)\n";
echo "   - العلامات التجارية: " . \App\Models\Brand::count() . " (يجب أن يكون 0)\n";
echo "   - المنتجات: " . \App\Models\Product::count() . " (يجب أن يكون 0)\n";
echo "   - الطلبات: " . \App\Models\Order::count() . " (يجب أن يكون 0)\n";

// 5. ملخص نهائي
echo "\n📋 ملخص الإعداد النهائي:\n";
echo "✅ الدول: " . \App\Models\Country::count() . "\n";
echo "✅ المستخدمين: " . \App\Models\User::count() . "\n";
echo "✅ باقي الجداول فارغة ✓\n";

echo "\n🔑 بيانات تسجيل الدخول:\n";
echo "   الإداري: <EMAIL> / password\n";
echo "   المستخدم: <EMAIL> / password\n";

echo "\n🎉 الإعداد المبسط جاهز تماماً!\n";

/**
 * للتشغيل:
 * 
 * 1. تشغيل المايجريشن والسيدرز:
 *    php artisan migrate:fresh --seed
 * 
 * 2. اختبار الإعداد:
 *    php artisan tinker
 *    ثم انسخ والصق الكود أعلاه
 */
