{"name": "ion-rangeslider", "version": "2.3.1", "description": "Cool, comfortable and easily customizable range slider with many options and skin support", "homepage": "http://ionden.com/a/plugins/ion.rangeSlider/", "author": {"name": "<PERSON> (IonDen)", "email": "<EMAIL>", "url": "https://github.com/IonDen"}, "keywords": ["jquery-plugin", "ecosystem:jquery", "j<PERSON>y", "form", "input", "range", "slider", "rangeslider", "interface", "diapason", "ui", "noui", "skins"], "main": "./js/ion.rangeSlider.js", "directories": {"lib": "js"}, "repository": {"type": "git", "url": "git://github.com/IonDen/ion.rangeSlider.git"}, "bugs": {"url": "https://github.com/IonDen/ion.rangeSlider/issues"}, "license": "MIT", "peerDependencies": {"jquery": ">=1.8"}, "ignore": [".idea", "_tmp", "PSD", "bower.json"]}